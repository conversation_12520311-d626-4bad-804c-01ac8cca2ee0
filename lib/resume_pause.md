  if ((taskDetailModel.getTaskCommencementDate() != null) && (taskDetailModel.getTaskStoppedDate() == null)) {
//                rightControl.setText(getString(R.string.StopTask));
                timerRightControl.showButtonStop(View.VISIBLE);
                timerLeftControl.showButtonStart(View.GONE);
                //need configure pause
                if (taskDetailModel.getResumePauseItems().isEmpty()) {
                    //never paused >> show Pause
                    timerRightControl.configureButtonPause(getString(R.string.PauseTask), View.VISIBLE);
                }else {
                    //find last item
                    int maxid = 0;
                    for(ResumePauseItemModel item : taskDetailModel.getResumePauseItems()) {
                        int tempid = Integer.parseInt(item.getResumeOrderID());
                        if(tempid>maxid) {
                            //found it!
                            maxid = tempid;
                        }
                    }

                    ResumePauseItemModel lastItem = taskDetailModel.getResumePauseItems().where().equalTo(ResumePauseItemModel.RESUME_ORDER_ID_KEY, Integer.toString(maxid)).findFirst();

                    if (lastItem != null){
                        if (lastItem.getResumeDate()==null && lastItem.getPauseDate() != null) {
                            //this was started/paused  >> need to show pause
                            timerRightControl.configureButtonPause(getString(R.string.ResumeTask), View.VISIBLE);
                        }else if (lastItem.getResumeDate()!=null && lastItem.getPauseDate() != null) {
                            //this was resumed/paused last time >> need to show resume
                            timerRightControl.configureButtonPause(getString(R.string.ResumeTask), View.VISIBLE);
                        }else if (lastItem.getPauseDate() == null) {
                            //this was resumed but not paused last time >> need to show pause
                            timerRightControl.configureButtonPause(getString(R.string.PauseTask), View.VISIBLE);
                        }
                    }
                }

            }


private void processPauseEvent() {
        if ((taskDetailModel.getTaskCommencementDate() != null) && (taskDetailModel.getTaskStoppedDate() == null)) {
            if (taskDetailModel.getResumePauseItems().size()<=0){
                //no pause item >> insert new one

                DatabaseManager.getInstance(getContext()).processRealmDataUpdate(ActivityBase.mRealm,
                        new DatabaseRunnable(null) {
                            @Override
                            public void run() {
                                ResumePauseItemModel item = new ResumePauseItemModel();
                                item.setResumeOrderID("1");
                                item.setResumeDate(null);
                                item.setPauseDate(Calendar.getInstance(Constant.DEFAULT_TIMEZONE_INSTANCE, Constant.DEFAULT_LOCALE_INSTANCE).getTime());
                                taskDetailModel.getResumePauseItems().add(item);

                                timerRightControl.configureButtonPause(getString(R.string.ResumeTask), View.VISIBLE);

                            }
                        });

            }else {
                //exists an pause item >> find the last item >> update pause time stamp

                DatabaseManager.getInstance(getContext()).processRealmDataUpdate(ActivityBase.mRealm,
                        new DatabaseRunnable(null) {
                            @Override
                            public void run() {
                                int maxid = 0;
                                for(ResumePauseItemModel item : taskDetailModel.getResumePauseItems()) {
                                    int tempid = Integer.parseInt(item.getResumeOrderID());
                                    if(tempid>maxid) {
                                        //found it!
                                        maxid = tempid;
                                    }
                                }

                                ResumePauseItemModel lastItem = taskDetailModel.getResumePauseItems().where().equalTo(ResumePauseItemModel.RESUME_ORDER_ID_KEY, Integer.toString(maxid)).findFirst();
                                if (lastItem != null){
                                    lastItem.setPauseDate(Calendar.getInstance(Constant.DEFAULT_TIMEZONE_INSTANCE, Constant.DEFAULT_LOCALE_INSTANCE).getTime());
                                }

                                timerRightControl.configureButtonPause(getString(R.string.ResumeTask), View.VISIBLE);
                            }
                        });

            }

        }

    }

     private void processResumeEvent() {
        if ((taskDetailModel.getTaskCommencementDate() != null) && (taskDetailModel.getTaskStoppedDate() == null)) {
            if (taskDetailModel.getResumePauseItems().size()<=0){
                //error => there should be at least 1 item because resume happens after pause physically

            }else {
                //pause item exist already >> find current pause item and its resume_order_id, and insert new one with +1
                DatabaseManager.getInstance(getContext()).processRealmDataUpdate(ActivityBase.mRealm,
                        new DatabaseRunnable(null) {
                            @Override
                            public void run() {
                                int maxid = 0;
                                for(ResumePauseItemModel item : taskDetailModel.getResumePauseItems()) {
                                    int tempid = Integer.parseInt(item.getResumeOrderID());
                                    if(tempid>maxid) {
                                        //found it!
                                        maxid = tempid;
                                    }
                                }

                                int nextid = maxid + 1;
                                ResumePauseItemModel item = new ResumePauseItemModel();
                                item.setResumeOrderID(Integer.toString(nextid));
                                item.setResumeDate(Calendar.getInstance(Constant.DEFAULT_TIMEZONE_INSTANCE, Constant.DEFAULT_LOCALE_INSTANCE).getTime());
                                item.setPauseDate(null);
                                taskDetailModel.getResumePauseItems().add(item);
                                timerRightControl.configureButtonPause(getString(R.string.PauseTask), View.VISIBLE);
                            }
                        });

            }

        }

        //debug
        CommonFunction.print(taskDetailModel.getResumePauseItems().toString());
    }