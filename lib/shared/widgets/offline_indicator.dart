import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gap/gap.dart';
import 'package:storetrack_app/config/themes/app_colors.dart';
import 'package:storetrack_app/core/extensions/theme_extensions.dart';
import 'package:storetrack_app/di/service_locator.dart';
import 'package:storetrack_app/shared/cubits/connectivity_cubit.dart';

/// A reusable widget that displays "Working offline" when the device is offline.
/// 
/// This widget automatically shows/hides based on the global connectivity state
/// managed by ConnectivityCubit. It can be customized to fit different design contexts.
class OfflineIndicator extends StatelessWidget {
  /// The background color of the offline indicator.
  /// Defaults to white.
  final Color backgroundColor;

  /// The text color of the offline indicator.
  /// Defaults to primary blue.
  final Color textColor;

  /// The padding around the offline indicator text.
  /// Defaults to symmetric vertical padding.
  final EdgeInsetsGeometry padding;

  /// The text to display when offline.
  /// Defaults to "Working offline".
  final String text;

  /// The font size of the text.
  /// Defaults to 12.
  final double fontSize;

  /// The color of the wifi off icon.
  /// Defaults to the same as textColor.
  final Color? iconColor;

  /// The size of the wifi off icon.
  /// Defaults to the same as fontSize.
  final double? iconSize;

  /// The spacing between the text and icon.
  /// Defaults to 8.0.
  final double spacing;

  /// Whether to check network connectivity automatically.
  /// Set to false in tests to avoid network dependency.
  /// Defaults to true.
  final bool checkNetworkConnectivity;

  /// Forces the indicator to be shown regardless of network status.
  /// Useful for testing or explicit offline mode.
  /// Defaults to false.
  final bool forceShow;

  // Set border radius
  final double borderRadius;

  /// Creates an offline indicator widget.
  const OfflineIndicator({
    super.key,
    this.backgroundColor = Colors.white,
    this.textColor = AppColors.primaryBlue,
    this.padding = const EdgeInsets.symmetric(vertical: 8.0),
    this.text = 'Working offline',
    this.fontSize = 12.0,
    this.iconColor,
    this.iconSize,
    this.spacing = 8.0,
    this.checkNetworkConnectivity = true,
    this.forceShow = false,
    this.borderRadius = 0,
  });

  /// Determines if the offline indicator should be shown based on connectivity state
  bool _shouldShowIndicator(ConnectivityState connectivityState) {
    // If explicitly forced to show, always show indicator
    if (forceShow) return true;

    // If network connectivity checking is disabled, don't show indicator
    if (!checkNetworkConnectivity) return false;

    // Otherwise, show indicator if connectivity state indicates offline
    return connectivityState is ConnectivityOffline;
  }

  @override
  Widget build(BuildContext context) {
    return BlocProvider.value(
      value: sl<ConnectivityCubit>(),
      child: BlocBuilder<ConnectivityCubit, ConnectivityState>(
        builder: (context, connectivityState) {
          final shouldShow = _shouldShowIndicator(connectivityState);
          
          if (!shouldShow) {
            return const SizedBox.shrink();
          }

          return Container(
            width: double.infinity,
            padding: padding,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(borderRadius),
              color: backgroundColor,
            ),
            child: Center(
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text(
                    text,
                    style: Theme.of(context).textTheme.montserratTitleExtraSmall.copyWith(
                      color: textColor,
                      fontSize: fontSize,
                    ),
                  ),
                  Gap(spacing),
                  Icon(
                    Icons.wifi_off,
                    color: iconColor ?? textColor,
                    size: iconSize ?? fontSize,
                  ),
                ],
              ),
            ),
          );
        },
      ),
    );
  }
}