import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gap/gap.dart';
import 'package:get_it/get_it.dart';
import 'package:storetrack_app/config/themes/app_colors.dart';
import 'package:storetrack_app/core/extensions/theme_extensions.dart';
import 'package:storetrack_app/features/home/<USER>/blocs/timer/timer_cubit.dart';
import 'package:storetrack_app/features/home/<USER>/blocs/timer/timer_state.dart';

class TimerBar extends StatefulWidget {
  final int taskId;
  
  const TimerBar({super.key, required this.taskId});

  @override
  State<TimerBar> createState() => _TimerBarState();
}

class _TimerBarState extends State<TimerBar> {
  late TimerCubit _timerCubit;

  @override
  void initState() {
    super.initState();
    _timerCubit = GetIt.instance<TimerCubit>();
    _timerCubit.loadTimerState(widget.taskId);
  }

  String _formatElapsedTime(Duration elapsed) {
    final minutes = elapsed.inMinutes;
    final seconds = elapsed.inSeconds.remainder(60);
    
    if (minutes > 0) {
      return "${minutes}m ${seconds}s";
    }
    return "${seconds}s";
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.fromLTRB(16, 8, 16, 8),
      decoration: BoxDecoration(
        color: Colors.white,
        border: Border(
          bottom: BorderSide(color: AppColors.appBarBorderBlack, width: 0.5),
        ),
      ),
      child: BlocBuilder<TimerCubit, TimerState>(
        bloc: _timerCubit,
        builder: (context, state) {
          return _buildContent(state);
        },
      ),
    );
  }

  Widget _buildContent(TimerState state) {
    if (state is TimerRunning || state is TimerPaused) {
      return _buildRunningPausedView(state);
    } else if (state is TimerStopped) {
      return _buildStoppedView(state);
    } else if (state is TimerError) {
      return _buildErrorView(state);
    } else {
      return _buildInitialView();
    }
  }

  Widget _buildInitialView() {
    return InkWell(
      onTap: () => _timerCubit.startTimer(widget.taskId),
      borderRadius: BorderRadius.circular(10),
      child: Container(
        height: 40,
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(10),
          border: Border.all(color: AppColors.primaryBlue),
        ),
        child: Center(
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              const Icon(
                Icons.play_arrow_rounded,
                color: AppColors.primaryBlue,
              ),
              const Gap(8),
              Text(
                'Start timer',
                style: Theme.of(context).textTheme.montserratSemiBold.copyWith(
                      color: AppColors.primaryBlue,
                      fontSize: 14,
                    ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildRunningPausedView(TimerState state) {
    final isRunning = state is TimerRunning;
    final elapsed = state is TimerRunning ? state.elapsed : 
                   state is TimerPaused ? state.elapsed : Duration.zero;

    return SizedBox(
      height: 40,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Text(
                'Time elapsed',
                style: Theme.of(context)
                    .textTheme
                    .montserratFormsField
                    .copyWith(color: AppColors.blackTint1),
              ),
              Text(
                _formatElapsedTime(elapsed),
                style: Theme.of(context)
                    .textTheme
                    .montserratTitleSmall
                    .copyWith(
                        color: AppColors.black,
                        letterSpacing: 0.5,
                        fontSize: 14),
              ),
            ],
          ),
          Row(
            children: [
              _buildControlButton(
                icon: isRunning ? Icons.pause : Icons.play_arrow,
                onTap: isRunning 
                    ? () => _timerCubit.pauseTimer(widget.taskId)
                    : () => _timerCubit.resumeTimer(widget.taskId),
                backgroundColor:
                    isRunning ? AppColors.warningBgColor : AppColors.green15,
                iconColor:
                    isRunning ? AppColors.richOrange : AppColors.loginGreen,
              ),
              const Gap(12),
              _buildControlButton(
                icon: Icons.stop,
                onTap: () => _timerCubit.stopTimer(widget.taskId),
                backgroundColor: AppColors.loginRed.withOpacity(0.15),
                iconColor: AppColors.loginRed,
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildStoppedView(TimerStopped state) {
    return SizedBox(
      height: 40,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Text(
                'Task completed',
                style: Theme.of(context)
                    .textTheme
                    .montserratFormsField
                    .copyWith(color: AppColors.blackTint1),
              ),
              Text(
                'Total time: ${_formatElapsedTime(state.totalElapsed)}',
                style: Theme.of(context)
                    .textTheme
                    .montserratTitleSmall
                    .copyWith(
                        color: AppColors.black,
                        letterSpacing: 0.5,
                        fontSize: 14),
              ),
            ],
          ),
          if (state.canStart)
            _buildControlButton(
              icon: Icons.play_arrow,
              onTap: () => _timerCubit.startTimer(widget.taskId),
              backgroundColor: AppColors.green15,
              iconColor: AppColors.loginGreen,
            ),
        ],
      ),
    );
  }

  Widget _buildErrorView(TimerError state) {
    return SizedBox(
      height: 40,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Expanded(
            child: Text(
              'Timer error: ${state.message}',
              style: Theme.of(context)
                  .textTheme
                  .montserratFormsField
                  .copyWith(color: AppColors.loginRed),
            ),
          ),
          _buildControlButton(
            icon: Icons.refresh,
            onTap: () => _timerCubit.loadTimerState(widget.taskId),
            backgroundColor: AppColors.loginRed.withOpacity(0.15),
            iconColor: AppColors.loginRed,
          ),
        ],
      ),
    );
  }

  Widget _buildControlButton({
    required IconData icon,
    required VoidCallback onTap,
    required Color backgroundColor,
    required Color iconColor,
  }) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(10),
      child: Container(
        width: 36,
        height: 36,
        decoration: BoxDecoration(
          color: backgroundColor,
          borderRadius: BorderRadius.circular(10),
        ),
        child: Icon(icon, color: iconColor),
      ),
    );
  }
}
