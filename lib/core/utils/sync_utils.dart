import 'dart:convert';
import 'dart:io';

import 'package:dio/dio.dart';
import 'package:realm/realm.dart';
import 'package:storetrack_app/core/database/realm_database.dart';
import 'package:storetrack_app/core/network/api_client.dart';
import 'package:storetrack_app/core/storage/data_manager.dart';
import 'package:storetrack_app/core/utils/logger.dart';
import 'package:storetrack_app/core/utils/photo_utils.dart';
import 'package:storetrack_app/core/utils/signature_utils.dart';
import 'package:storetrack_app/features/home/<USER>/mappers/task_detail_mapper.dart';
import 'package:storetrack_app/features/home/<USER>/models/task_detail_model.dart';
import 'package:storetrack_app/features/home/<USER>/entities/upload_photo_request_entity.dart';
import 'package:storetrack_app/features/home/<USER>/entities/upload_photo_response_entity.dart';
import 'package:storetrack_app/features/home/<USER>/entities/upload_sign_request_entity.dart';
import 'package:storetrack_app/features/home/<USER>/entities/upload_signature_response_entity.dart';
import 'package:storetrack_app/features/home/<USER>/entities/sync_pic_request_entity.dart'
    as sync_pic;
import 'package:storetrack_app/features/home/<USER>/entities/sync_sign_request_entity.dart'
    as sync_sign;
import 'package:storetrack_app/features/home/<USER>/entities/submit_report_request_entity.dart'
    as submit_report;
import 'package:storetrack_app/features/home/<USER>/entities/tasks_response_entity.dart';
import 'package:storetrack_app/features/home/<USER>/entities/tasks_request_entity.dart';
import 'package:storetrack_app/features/home/<USER>/entities/photo_type_list_entity.dart';
import 'package:storetrack_app/features/home/<USER>/entities/photo_type_delete_list_entity.dart';
import 'package:storetrack_app/features/home/<USER>/entities/signature_type_list_entity.dart';
import 'package:storetrack_app/features/home/<USER>/entities/signature_type_delete_list_entity.dart';
import 'package:storetrack_app/di/service_locator.dart';

/// Utility class for sync-related operations
///
/// This class provides helper methods for creating API request entities
/// and processing data transformations needed for the four new sync endpoints:
///
/// 1. `/send_task_pic_v4_11` - Upload individual photos
/// 2. `/sync_pic_info_mpt` - Sync photo metadata after all uploads
/// 3. `/send_task_sig_v4_11` - Upload individual signatures
/// 4. `/sync_sig_info` - Sync signature metadata after all uploads
///
/// Usage example:
/// ```dart
/// // 1. Upload individual photos first
/// final photosToUpload = SyncUtils.getPhotosToUpload(tasks);
/// for (final photoData in photosToUpload) {
///   final request = await SyncUtils.createUploadPhotoRequest(
///     photo: photoData['photo'],
///     taskId: photoData['taskId'],
///     folderId: photoData['folderId'],
///   );
///   final result = await homeRepository.uploadPhoto(request);
/// }
///
/// // 2. Then sync photo metadata
/// final syncRequest = await SyncUtils.createSyncPicInfoRequest(tasks: tasks);
/// final syncResult = await homeRepository.syncPhotoInfo(syncRequest);
/// ```
class SyncUtils {
  static const String appVersion = "9.9.9";

  /// Get device UID from DataManager
  static Future<String> getDeviceUid() async {
    final dataManager = sl<DataManager>();
    return await dataManager.getOrCreateDeviceId();
  }

  /// Encode file to base64 string
  ///
  /// Supports both local file paths and URLs. If the input is a URL (starts with http/https),
  /// it will download the content first and then encode it to base64.
  ///
  /// Parameters:
  /// - [filePath]: Local file path or URL to encode
  ///
  /// Returns base64 encoded string
  ///
  /// Throws:
  /// - [ArgumentError] if filePath is null or empty
  /// - [FileSystemException] if local file doesn't exist or can't be read
  /// - [DioException] if URL download fails
  /// - [FormatException] if the input is not a valid file path or URL
  static Future<String> encodeFileToBase64(String filePath) async {
    // Input validation
    if (filePath.isEmpty) {
      throw ArgumentError('File path cannot be empty');
    }

    try {
      // Check if input is a URL
      final isUrl =
          filePath.startsWith('http://') || filePath.startsWith('https://');

      List<int> bytes;

      if (isUrl) {
        // Handle URL download
        logger('Downloading file from URL: $filePath');
        bytes = await _downloadFileFromUrl(filePath);
      } else {
        // Handle local file path
        logger('Reading local file: $filePath');
        final file = File(filePath);

        // Check if file exists
        if (!await file.exists()) {
          throw FileSystemException('File not found: $filePath');
        }

        bytes = await file.readAsBytes();
      }

      // Encode to base64
      final base64String = base64Encode(bytes);
      logger('Successfully encoded ${bytes.length} bytes to base64');
      return base64String;
    } catch (e) {
      logger('Error encoding file to base64: $e');
      rethrow;
    }
  }

  /// Download file from URL and return bytes
  ///
  /// Private helper method to download content from URLs using Dio client
  static Future<List<int>> _downloadFileFromUrl(String url) async {
    try {
      // Get Dio instance from ApiClient
      final apiClient = sl<ApiClient>();
      final dio = apiClient.instance;

      // Download file with response type bytes
      final response = await dio.get(
        url,
        options: Options(
          responseType: ResponseType.bytes,
          followRedirects: true,
          validateStatus: (status) => status != null && status < 400,
        ),
      );

      if (response.statusCode == 200) {
        return response.data as List<int>;
      } else {
        throw DioException(
          requestOptions: response.requestOptions,
          response: response,
          message: 'Failed to download file: HTTP ${response.statusCode}',
        );
      }
    } catch (e) {
      if (e is DioException) {
        throw DioException(
          requestOptions: e.requestOptions,
          response: e.response,
          message: 'Network error downloading file from $url: ${e.message}',
        );
      } else {
        throw Exception('Unexpected error downloading file from $url: $e');
      }
    }
  }

  /// Create upload photo request entity from photo data
  static Future<UploadPhotoRequestEntity> createUploadPhotoRequest({
    required Photo photo,
    required int taskId,
    required int folderId,
  }) async {
    final dataManager = sl<DataManager>();

    final request = UploadPhotoRequestEntity();
    request.token = await dataManager.getAuthToken();
    request.userId = int.parse(await dataManager.getUserId() ?? "0");
    request.taskId = taskId;
    request.folderId = folderId;
    request.photoId = photo.photoId?.toInt();
    request.photoDate = null;
    request.photoCaption = photo.caption;
    request.cannotUploadMandatory = photo.cannotUploadMandatory;
    request.formId = photo.formId?.toInt();
    request.questionId = photo.questionId?.toInt();
    request.measurementId = photo.measurementId?.toInt();
    request.questionpartId = photo.questionpartId?.toInt();
    request.questionPartMultiId = photo.questionPartMultiId;
    request.measurementPhototypeId = photo.measurementPhototypeId?.toInt();
    request.deviceuid = await dataManager.getOrCreateDeviceId();

    if (photo.cannotUploadMandatory == true) {
      request.pictureBlob = "-1";
    } else {
      // Use localPath if available, otherwise use photoUrl
      final imagePath = photo.localPath ?? photo.photoUrl ?? "";
      request.pictureBlob = await encodeFileToBase64(imagePath);
    }

    return request;
  }

  /// Create upload signature request entity from signature data
  static Future<UploadSignRequestEntity> createUploadSignatureRequest({
    required Signature signature,
    required int taskId,
    required int folderId,
  }) async {
    final dataManager = sl<DataManager>();

    final request = UploadSignRequestEntity();
    request.token = await dataManager.getAuthToken();
    request.userId = int.parse(await dataManager.getUserId() ?? "0");
    request.taskId = taskId;
    request.folderId = folderId;
    request.signatureId = signature.signatureId?.toInt();
    request.signedBy = signature.signedBy;
    request.cannotUploadMandatory = signature.cannotUploadMandatory;
    request.formId = signature.formId?.toInt();
    request.questionId = signature.questionId?.toInt();
    request.deviceuid = await dataManager.getOrCreateDeviceId();

    if (signature.cannotUploadMandatory == true) {
      request.signatureBlob = "-1";
    } else {
      // Use localPath if available, otherwise use signatureUrl
      final signaturePath = signature.localPath ?? signature.signatureUrl ?? "";
      request.signatureBlob = await encodeFileToBase64(signaturePath);
    }

    return request;
  }

  /// Create sync pic info request entity for photo metadata sync
  static Future<sync_pic.SyncPicInfoRequestEntity> createSyncPicInfoRequest({
    required List<TaskDetail> tasks,
  }) async {
    final dataManager = sl<DataManager>();

    final syncPicRequest = sync_pic.SyncPicInfoRequestEntity();
    final List<sync_pic.Task> tasksSync = [];

    for (var task in tasks) {
      final List<sync_pic.PhotoFolder> photoFolderList = [];

      for (var photoFolder in task.photoFolder ?? []) {
        final deletePhotosIds = <int>[];

        for (var photo in photoFolder.photos ?? []) {
          if (photo.userDeletedPhoto == true) {
            deletePhotosIds.add(photo.photoId?.toInt() ?? 0);
          }
        }

        if (deletePhotosIds.isNotEmpty) {
          photoFolderList.add(sync_pic.PhotoFolder(
            folderId: photoFolder.folderId?.toInt(),
            deletePhotosIds: deletePhotosIds.join(","),
          ));
        }
      }

      if (photoFolderList.isNotEmpty) {
        tasksSync.add(sync_pic.Task(
          taskId: task.taskId?.toInt(),
          uploadPhotosSuccess: true,
          modifiedTimeStampPhotos: task.modifiedTimeStampPhotos,
          photoFolder: photoFolderList,
        ));
      }
    }

    syncPicRequest.tasks = tasksSync;
    syncPicRequest.token = await dataManager.getAuthToken();
    syncPicRequest.userId = int.parse(await dataManager.getUserId() ?? "0");
    syncPicRequest.deviceuid = await dataManager.getOrCreateDeviceId();

    return syncPicRequest;
  }

  /// Create sync signature info request entity for signature metadata sync
  static Future<sync_sign.SyncSignInfoRequestEntity>
      createSyncSignatureInfoRequest({
    required List<TaskDetail> tasks,
  }) async {
    final dataManager = sl<DataManager>();

    final syncSignRequest = sync_sign.SyncSignInfoRequestEntity();
    final List<sync_sign.Task> tasksSync = [];

    for (var task in tasks) {
      final List<sync_sign.SignatureFolder> signatureFolderList = [];

      for (var signatureFolder in task.signatureFolder ?? []) {
        final deleteSignatureIds = <int>[];

        for (var signature in signatureFolder.signatures ?? []) {
          if (signature.userDeletedSignature == true) {
            deleteSignatureIds.add(signature.signatureId?.toInt() ?? 0);
          }
        }

        if (deleteSignatureIds.isNotEmpty) {
          signatureFolderList.add(sync_sign.SignatureFolder(
            folderId: signatureFolder.folderId?.toInt(),
            deleteSignaturesIds: deleteSignatureIds.join(","),
          ));
        }
      }

      if (signatureFolderList.isNotEmpty) {
        tasksSync.add(sync_sign.Task(
          taskId: task.taskId?.toInt(),
          uploadSignatureSuccess: true,
          modifiedTimeStampSignatures: task.modifiedTimeStampSignatures,
          signatureFolder: signatureFolderList,
        ));
      }
    }

    syncSignRequest.tasks = tasksSync;
    syncSignRequest.token = await dataManager.getAuthToken();
    syncSignRequest.userId = int.parse(await dataManager.getUserId() ?? "0");
    syncSignRequest.deviceuid = await dataManager.getOrCreateDeviceId();

    return syncSignRequest;
  }

  /// Get all photos that need to be uploaded from tasks
  static List<Map<String, dynamic>> getPhotosToUpload(List<TaskDetail> tasks) {
    final List<Map<String, dynamic>> photosToUpload = [];

    for (var task in tasks) {
      for (var photoFolder in task.photoFolder ?? []) {
        for (var photo in photoFolder.photos ?? []) {
          // Only include photos that haven't been deleted and have content
          if (photo.userDeletedPhoto != true &&
              (photo.isEdited == true || photo.localPath != null)) {
            photosToUpload.add({
              'photo': photo,
              'taskId': task.taskId?.toInt() ?? 0,
              'folderId': photoFolder.folderId?.toInt() ?? 0,
            });
          }
        }
      }
    }

    return photosToUpload;
  }

  /// Get all signatures that need to be uploaded from tasks
  static List<Map<String, dynamic>> getSignaturesToUpload(
      List<TaskDetail> tasks) {
    final List<Map<String, dynamic>> signaturesToUpload = [];

    for (var task in tasks) {
      for (var signatureFolder in task.signatureFolder ?? []) {
        for (var signature in signatureFolder.signatures ?? []) {
          // Only include signatures that haven't been deleted and have content
          if (signature.userDeletedSignature != true &&
              (signature.isEdited == true || signature.localPath != null)) {
            signaturesToUpload.add({
              'signature': signature,
              'taskId': task.taskId?.toInt() ?? 0,
              'folderId': signatureFolder.folderId?.toInt() ?? 0,
            });
          }
        }
      }
    }

    return signaturesToUpload;
  }

  /// Check if a given `photoId` already exists inside any of the photo folders
  /// of the provided [task].
  ///
  /// The API that handles `/api/send_task_pic_v4_11` returns the definitive
  /// `photoId` generated by the server.  When the same image is uploaded twice
  /// (user selects the same image again, or device retries a previous upload),
  /// the backend may respond with an **existing** `photoId` instead of a newly
  /// created one.  This helper makes it easy to detect that situation so that
  /// callers can decide to ignore the duplicate or clean up the temporary
  /// local record.
  ///
  /// Returns `true` if any photo inside `task.photoFolder` has `photoId` equal
  /// to [serverPhotoId]; otherwise returns `false`.
  static bool isDuplicatePhoto({
    required TaskDetail task,
    required int serverPhotoId,
  }) {
    if (serverPhotoId == 0) return false;

    for (final photoFolder in task.photoFolder ?? []) {
      for (final photo in photoFolder.photos ?? []) {
        if ((photo.photoId ?? 0).toInt() == serverPhotoId) {
          return true;
        }
      }
    }
    return false;
  }

  /// Handles photo upload response with duplicate detection and local storage optimization
  ///
  /// This method processes the server response from photo upload API and:
  /// 1. Checks if the uploaded photo is a duplicate (already exists in database)
  /// 2. If duplicate: Deletes the duplicate photo record from database
  /// 3. If not duplicate: Updates the local photo record with server data and deletes local file
  ///
  /// Parameters:
  /// - [uploadResponse]: The response from the photo upload API
  /// - [taskId]: The ID of the task containing the photo
  /// - [originalLocalPath]: The original local path of the uploaded photo (for matching)
  ///
  /// Returns `true` if processing was successful, `false` otherwise
  static Future<bool> handlePhotoUploadResponse({
    required UploadPhotoResponseEntity uploadResponse,
    required int taskId,
    required String originalLocalPath,
  }) async {
    try {
      final serverPhoto = uploadResponse.data;
      if (serverPhoto == null) {
        logger('Invalid server photo response: data is null');
        return false;
      }

      final serverPhotoId = serverPhoto.photoId?.toInt() ?? 0;

      if (serverPhotoId == 0) {
        logger('Invalid server photo ID received');
        return false;
      }

      // Check for duplicate photo using PhotoUtils
      final isDuplicate = await PhotoUtils.photoExists(
        taskId: taskId,
        photoId: serverPhotoId,
      );

      if (isDuplicate) {
        // Handle duplicate: delete the duplicate photo record
        logger(
            'Duplicate photo detected with ID: $serverPhotoId, deleting local record');
        return await PhotoUtils.deletePhotoRecord(
          taskId: taskId,
          localPath: originalLocalPath,
          deleteLocalFile: true,
        );
      } else {
        // Handle successful upload: update photo record and delete local file
        logger('Updating photo record with server data for ID: $serverPhotoId');
        return await PhotoUtils.updatePhotoWithServerData(
          taskId: taskId,
          localPath: originalLocalPath,
          photoId: serverPhotoId,
          photoUrl: serverPhoto.photoUrl,
          caption: serverPhoto.photoCaption,
          cannotUploadMandatory: serverPhoto.cannotUploadMandatory,
          modifiedTimeStamp: serverPhoto.modifiedTimeStampPhoto,
          deleteLocalFile: true,
        );
      }
    } catch (e) {
      logger('Error handling photo upload response: $e');
      return false;
    }
  }

  /// Handles signature upload response with duplicate detection and local storage optimization
  ///
  /// This method processes the server response from signature upload API and:
  /// 1. Checks if the uploaded signature is a duplicate (already exists in database)
  /// 2. If duplicate: Deletes the duplicate signature record from database
  /// 3. If not duplicate: Updates the local signature record with server data and deletes local file
  ///
  /// Parameters:
  /// - [uploadResponse]: The response from the signature upload API
  /// - [taskId]: The ID of the task containing the signature
  /// - [originalLocalPath]: The original local path of the uploaded signature (for matching)
  ///
  /// Returns `true` if processing was successful, `false` otherwise
  static Future<bool> handleSignatureUploadResponse({
    required UploadSignatureResponseEntity uploadResponse,
    required int taskId,
    required String originalLocalPath,
  }) async {
    try {
      final serverSignature = uploadResponse.data;
      if (serverSignature == null) {
        logger('Invalid server signature response: data is null');
        return false;
      }

      final serverSignatureId = serverSignature.signatureId?.toInt() ?? 0;

      if (serverSignatureId == 0) {
        logger('Invalid server signature ID received');
        return false;
      }

      // Check for duplicate signature using SignatureUtils
      final isDuplicate = await SignatureUtils.signatureExists(
        taskId: taskId,
        signatureId: serverSignatureId,
      );

      if (isDuplicate) {
        // Handle duplicate: delete the duplicate signature record
        logger(
            'Duplicate signature detected with ID: $serverSignatureId, deleting local record');
        return await SignatureUtils.deleteSignatureRecord(
          taskId: taskId,
          localPath: originalLocalPath,
          deleteLocalFile: true,
        );
      } else {
        // Handle successful upload: update signature record and delete local file
        logger(
            'Updating signature record with server data for ID: $serverSignatureId');
        return await SignatureUtils.updateSignatureWithServerData(
          taskId: taskId,
          localPath: originalLocalPath,
          signatureId: serverSignatureId,
          signatureUrl: serverSignature.signatureUrl,
          signedBy: serverSignature.signedBy,
          cannotUploadMandatory: serverSignature.cannotUploadMandatory,
          modifiedTimeStamp: serverSignature.modifiedTimeStampSignature,
          deleteLocalFile: true,
        );
      }
    } catch (e) {
      logger('Error handling signature upload response: $e');
      return false;
    }
  }

  /// Create submit report request entity from task data
  ///
  /// This method prepares the request body for the submit report API,
  /// taking inspiration from the submitReportData() method in syncing.dart.
  /// It structures the request with all necessary task data including forms,
  /// question answers, and followup tasks.
  ///
  /// Parameters:
  /// - [task]: The task detail containing all data to be submitted
  ///
  /// Returns a properly structured SubmitReportRequestEntity
  static Future<submit_report.SubmitReportRequestEntity>
      createSubmitReportRequest({
    required TaskDetail task,
  }) async {
    final dataManager = sl<DataManager>();
    final String actualDeviceUid = await dataManager.getOrCreateDeviceId();
    const String actualAppVersion = "9.9.9";

    final request = submit_report.SubmitReportRequestEntity();
    request.token = await dataManager.getAuthToken();
    request.userId = await dataManager.getUserId();
    request.deviceUid = actualDeviceUid;
    request.appversion = actualAppVersion;
    request.taskId = task.taskId.toString();
    request.comment = task.comment;
    request.minutes = task.minutes?.toInt();
    request.timerMinutes = 0;
    request.claimableKms = task.claimableKms?.toInt();
    request.pages = task.pages?.toInt();
    request.taskStatus = task.taskStatus;
    request.submissionState = task.submissionState?.toInt();
    request.taskCommencementTimeStamp = task.taskCommencementTimeStamp;
    request.taskStoppedTimeStamp = task.taskStoppedTimeStamp;
    request.scheduledTimeStamp = task.scheduledTimeStamp;
    request.submissionTimeStamp = DateTime.now();
    request.startTaskLatitude = task.taskLatitude?.toInt();
    request.startTaskLongitude = task.taskLongitude?.toInt();
    request.taskLatitude = task.latitude?.toInt();
    request.taskLongitude = task.longitude?.toInt();
    request.budgetCalculated = 0;

    // Initialize lists
    request.forms = [];
    request.followupTasks = [];
    request.resumePauseItems = [];

    // Process forms and question answers
    for (var form in task.forms ?? []) {
      var formPost = Form();
      formPost.formId = form.formId;
      formPost.questionAnswers = [];

      for (var questionAnswer in form.questionAnswers ?? []) {
        if ((questionAnswer.isComment == false) ||
            (questionAnswer.isComment == true &&
                questionAnswer.measurementTextResult != null)) {
          var questionAnswerPost = QuestionAnswer(
            questionId: questionAnswer.questionId,
            questionpartId: questionAnswer.questionpartId,
            questionPartMultiId: questionAnswer.questionPartMultiId,
            measurementId: questionAnswer.measurementId,
            measurementTypeId: questionAnswer.measurementTypeId,
            measurementOptionId: questionAnswer.measurementOptionId,
            measurementOptionIds: questionAnswer.measurementOptionIds,
            measurementTextResult: questionAnswer.measurementTextResult,
            isComment: questionAnswer.isComment,
            commentTypeId: questionAnswer.commentTypeId,
          );
          formPost.questionAnswers?.add(questionAnswerPost);
        }
      }

      if (formPost.questionAnswers?.isNotEmpty == true) {
        request.forms?.add(formPost);
      }
    }

    // Process followup tasks
    for (var followupTask in task.followupTasks ?? []) {
      var followupTaskPost = submit_report.FollowupTask();
      followupTaskPost.taskId = task.taskId.toString();
      followupTaskPost.visitDate = followupTask.selectedVisitDate;
      followupTaskPost.followupTypeId = 0;
      followupTaskPost.followupItemId = 0;
      followupTaskPost.budget = followupTask.selectedBudget?.toInt();
      followupTaskPost.scheduleNote = followupTask.selectedScheduleNote;
      followupTaskPost.followupNumber = followupTask.followupNumber?.toInt();
      request.followupTasks?.add(followupTaskPost);
    }

    return request;
  }

  /// Get all tasks that need to be submitted (have sync pending status)
  ///
  /// Returns a list of TaskDetail entities that are marked for sync
  static List<TaskDetail> getTasksToSubmit() {
    final realm = RealmDatabase.instance.realm;
    final taskModels = realm.all<TaskDetailModel>();

    // Convert models to entities
    final tasks =
        taskModels.map((model) => TaskDetailMapper.toEntity(model)).toList();

    return tasks.where((task) => task.syncPending == true).toList();
  }

  /// Process TasksResponseEntity and update local database
  ///
  /// This method handles all response fields from the getTasks API:
  /// - delete_task_ids: Delete tasks from local database
  /// - update_tasks_documents: Update document-related data for existing tasks
  /// - update_tasks_forms: Update form-related data for existing tasks
  /// - update_task_members: Update member/helper data for existing tasks
  /// - update_tasks_tasks: Update general task data for existing tasks
  /// - update_tasks_photos: Update photo-related data for existing tasks
  /// - add_tasks: Add new tasks to local database
  /// - update_tasks_submission: Update submission-related data for existing tasks
  /// - update_tasks_signatures: Update signature-related data for existing tasks
  /// - upload_task_ids: Process uploaded task IDs for sync status updates
  ///
  /// Returns true if processing was successful, false otherwise
  static Future<bool> processTasksResponse(TasksResponseEntity response) async {
    try {
      final realm = sl<RealmDatabase>().realm;

      // Process each response field in order following process_core.md 15 steps
      // Step 11-10: Delete tasks
      await _processDeleteTaskIds(realm, response.deleteTaskIds);
      // Step 11-2: Update task documents
      await _processUpdateTasksDocuments(realm, response.updateTasksDocuments);
      // Step 11-3: Update tasks forms
      await _processUpdateTasksForms(realm, response.updateTasksForms);
      // Step 11-6: Update task members
      await _processUpdateTaskMembers(realm, response.updateTaskMembers);
      // Step 11-4: Update tasks information/tasks
      await _processUpdateTasksTasks(realm, response.updateTasksTasks);
      // Step 11-4: Update tasks photos
      await _processUpdateTasksPhotos(realm, response.updateTasksPhotos);
      // Step 11-7: Update phototypes
      await _processUpdatePhototypes(realm, response.updatePhototypes);
      // Step 11-8: Add phototypes
      await _processAddPhototypes(realm, response.addPhototypes);
      // Step 11-9: Delete phototypes
      await _processDeletePhototypes(realm, response.deletePhototypes);
      // Step 11-1: Add new tasks
      await _processAddTasks(realm, response.addTasks);
      // Step 11-5: Update task submission
      await _processUpdateTasksSubmission(
          realm, response.updateTasksSubmission);
      // Step 12-4: Update tasks signatures
      await _processUpdateTasksSignatures(
          realm, response.updateTasksSignatures);
      // Step 12-7: Update signature types
      await _processUpdateSignatureTypes(realm, response.updateSignatureTypes);
      // Step 12-8: Add signature types
      await _processAddSignatureTypes(realm, response.addSignatureTypes);
      // Step 12-9: Delete signature types
      await _processDeleteSignatureTypes(realm, response.deleteSignatureTypes);
      // Process uploaded task IDs
      await _processUploadTaskIds(realm, response.uploadTaskIds);

      logger('✅ TasksResponse processing completed successfully');
      return true;
    } catch (e) {
      logger('❌ Error processing TasksResponse: $e');
      return false;
    }
  }

  /// Process delete_task_ids: Remove tasks from local database
  static Future<void> _processDeleteTaskIds(
      Realm realm, List<String>? deleteTaskIds) async {
    if (deleteTaskIds == null || deleteTaskIds.isEmpty) return;

    logger('🗑️ Processing ${deleteTaskIds.length} task deletions');

    realm.write(() {
      for (final taskIdStr in deleteTaskIds) {
        final taskId = int.tryParse(taskIdStr);
        if (taskId == null) {
          logger('⚠️ Invalid task ID format: $taskIdStr');
          continue;
        }

        final task =
            realm.query<TaskDetailModel>('taskId == \$0', [taskId]).firstOrNull;
        if (task != null) {
          realm.delete(task);
          logger('🗑️ Deleted task: $taskId');
        } else {
          logger('⚠️ Task not found for deletion: $taskId');
        }
      }
    });
  }

  /// Process update_tasks_documents: Update document-related data for existing tasks
  static Future<void> _processUpdateTasksDocuments(
      Realm realm, List<TaskDetail>? updateTasksDocuments) async {
    if (updateTasksDocuments == null || updateTasksDocuments.isEmpty) return;

    logger('📄 Processing ${updateTasksDocuments.length} document updates');

    realm.write(() {
      for (final taskUpdate in updateTasksDocuments) {
        final taskId = taskUpdate.taskId?.toInt();
        if (taskId == null) continue;

        final existingTask =
            realm.query<TaskDetailModel>('taskId == \$0', [taskId]).firstOrNull;
        if (existingTask != null) {
          // Update document-related fields
          existingTask.documents.clear();
          if (taskUpdate.documents != null) {
            // Create a temporary task model to get the mapped documents
            final tempTaskModel = TaskDetailMapper.toModel(taskUpdate, 0);
            existingTask.documents.addAll(tempTaskModel.documents);
          }
          existingTask.modifiedTimeStampDocuments =
              taskUpdate.modifiedTimeStampDocuments;

          logger('📄 Updated documents for task: $taskId');
        } else {
          logger('⚠️ Task not found for document update: $taskId');
        }
      }
    });
  }

  /// Process update_tasks_forms: Update form-related data for existing tasks
  static Future<void> _processUpdateTasksForms(
      Realm realm, List<TaskDetail>? updateTasksForms) async {
    if (updateTasksForms == null || updateTasksForms.isEmpty) return;

    logger('📝 Processing ${updateTasksForms.length} form updates');

    realm.write(() {
      for (final taskUpdate in updateTasksForms) {
        final taskId = taskUpdate.taskId?.toInt();
        if (taskId == null) continue;

        final existingTask =
            realm.query<TaskDetailModel>('taskId == \$0', [taskId]).firstOrNull;
        if (existingTask != null) {
          // Update form-related fields
          existingTask.forms.clear();
          if (taskUpdate.forms != null) {
            // Create a temporary task model to get the mapped forms
            final tempTaskModel = TaskDetailMapper.toModel(taskUpdate, 0);
            existingTask.forms.addAll(tempTaskModel.forms);
          }
          existingTask.modifiedTimeStampForms =
              taskUpdate.modifiedTimeStampForms;
          existingTask.ctFormsTotalCnt = taskUpdate.ctFormsTotalCnt?.toInt();
          existingTask.ctFormsCompletedCnt =
              taskUpdate.ctFormsCompletedCnt?.toInt();
          existingTask.kTotal = taskUpdate.kTotal?.toInt();
          existingTask.kCompleted = taskUpdate.kCompleted?.toInt();

          logger('📝 Updated forms for task: $taskId');
        } else {
          logger('⚠️ Task not found for form update: $taskId');
        }
      }
    });
  }

  /// Process update_task_members: Update member/helper data for existing tasks
  static Future<void> _processUpdateTaskMembers(
      Realm realm, List<TaskMembersHelper>? updateTaskMembers) async {
    if (updateTaskMembers == null || updateTaskMembers.isEmpty) return;

    logger('👥 Processing ${updateTaskMembers.length} member updates');

    realm.write(() {
      for (final memberUpdate in updateTaskMembers) {
        final taskId = memberUpdate.taskId;
        if (taskId == null) continue;

        final existingTask =
            realm.query<TaskDetailModel>('taskId == \$0', [taskId]).firstOrNull;
        if (existingTask != null) {
          // Update member-related fields
          existingTask.taskmembers.clear();
          if (memberUpdate.taskMembers != null) {
            // Create a temporary task detail with the members
            final tempTask = TaskDetail(taskmembers: memberUpdate.taskMembers);
            final tempTaskModel = TaskDetailMapper.toModel(tempTask, 0);
            existingTask.taskmembers.addAll(tempTaskModel.taskmembers);
          }
          existingTask.modifiedTimeStampMembers = DateTime.now();

          logger('👥 Updated members for task: $taskId');
        } else {
          logger('⚠️ Task not found for member update: $taskId');
        }
      }
    });
  }

  /// Process update_tasks_tasks: Update general task data for existing tasks
  static Future<void> _processUpdateTasksTasks(
      Realm realm, List<TaskDetail>? updateTasksTasks) async {
    if (updateTasksTasks == null || updateTasksTasks.isEmpty) return;

    logger('📋 Processing ${updateTasksTasks.length} task updates');

    realm.write(() {
      for (final taskUpdate in updateTasksTasks) {
        final taskId = taskUpdate.taskId?.toInt();
        if (taskId == null) continue;

        final existingTask =
            realm.query<TaskDetailModel>('taskId == \$0', [taskId]).firstOrNull;
        if (existingTask != null) {
          // Update general task fields
          existingTask.projectId = taskUpdate.projectId?.toInt();
          existingTask.scheduleId = taskUpdate.scheduleId?.toInt();
          existingTask.sentToPayroll = taskUpdate.sentToPayroll;
          existingTask.showKm = taskUpdate.showKm;
          existingTask.storeId = taskUpdate.storeId?.toInt();
          existingTask.client = taskUpdate.client;
          existingTask.clientId = taskUpdate.clientId?.toInt();
          existingTask.clientLogoUrl = taskUpdate.clientLogoUrl;
          existingTask.storeGroup = taskUpdate.storeGroup;
          existingTask.storeGroupId = taskUpdate.storeGroupId?.toInt();
          existingTask.storeName = taskUpdate.storeName;
          existingTask.storeEmail = taskUpdate.storeEmail;
          existingTask.minutes = taskUpdate.minutes?.toInt();
          existingTask.budget = taskUpdate.budget?.toInt();
          existingTask.originalbudget = taskUpdate.originalbudget?.toInt();
          existingTask.comment = taskUpdate.comment;
          existingTask.claimableKms = taskUpdate.claimableKms?.toInt();
          existingTask.flightDuration = taskUpdate.flightDuration?.toInt();
          existingTask.pages = taskUpdate.pages?.toInt();
          existingTask.location = taskUpdate.location;
          existingTask.suburb = taskUpdate.suburb;
          existingTask.latitude = taskUpdate.latitude?.toDouble();
          existingTask.longitude = taskUpdate.longitude?.toDouble();
          existingTask.taskLatitude = taskUpdate.taskLatitude?.toDouble();
          existingTask.taskLongitude = taskUpdate.taskLongitude?.toDouble();
          existingTask.cycle = taskUpdate.cycle;
          existingTask.cycleId = taskUpdate.cycleId?.toInt();
          existingTask.canDelete = taskUpdate.canDelete;
          existingTask.scheduledTimeStamp = taskUpdate.scheduledTimeStamp;
          existingTask.submissionTimeStamp = taskUpdate.submissionTimeStamp;
          existingTask.expires = taskUpdate.expires;
          existingTask.onTask = taskUpdate.onTask;
          existingTask.phone = taskUpdate.phone;
          existingTask.rangeStart = taskUpdate.rangeStart;
          existingTask.rangeEnd = taskUpdate.rangeEnd;
          existingTask.reOpened = taskUpdate.reOpened;
          existingTask.reOpenedReason = taskUpdate.reOpenedReason;
          existingTask.taskStatus = taskUpdate.taskStatus;
          existingTask.warehousejobId = taskUpdate.warehousejobId?.toInt();
          existingTask.connoteUrl = taskUpdate.connoteUrl;
          existingTask.posRequired = taskUpdate.posRequired;
          existingTask.isPosMandatory = taskUpdate.isPosMandatory;
          existingTask.posReceived = taskUpdate.posReceived;
          existingTask.posSentTo = taskUpdate.posSentTo;
          existingTask.posSentToEmail = taskUpdate.posSentToEmail;
          existingTask.modifiedTimeStampTask = taskUpdate.modifiedTimeStampTask;
          existingTask.modifiedTimeStampSignaturetypes =
              taskUpdate.modifiedTimeStampSignaturetypes;
          existingTask.modifiedTimeStampPhototypes =
              taskUpdate.modifiedTimeStampPhototypes;
          existingTask.taskCommencementTimeStamp =
              taskUpdate.taskCommencementTimeStamp;
          existingTask.taskStoppedTimeStamp = taskUpdate.taskStoppedTimeStamp;
          existingTask.teamlead = taskUpdate.teamlead?.toInt();
          existingTask.taskNote = taskUpdate.taskNote;
          existingTask.disallowReschedule = taskUpdate.disallowReschedule;
          existingTask.photoResPerc = taskUpdate.photoResPerc?.toInt();
          existingTask.liveImagesOnly = taskUpdate.liveImagesOnly;
          existingTask.timeSchedule = taskUpdate.timeSchedule;
          existingTask.scheduleTypeId = taskUpdate.scheduleTypeId?.toInt();
          existingTask.showFollowupIconMulti = taskUpdate.showFollowupIconMulti;
          existingTask.followupSelectedMulti = taskUpdate.followupSelectedMulti;
          existingTask.regionId = taskUpdate.regionId?.toInt();
          existingTask.isOpen = taskUpdate.isOpen;
          existingTask.taskCount = taskUpdate.taskCount?.toInt();
          existingTask.preftime = taskUpdate.preftime;
          existingTask.sendTo = taskUpdate.sendTo;
          existingTask.submissionState = taskUpdate.submissionState;
          existingTask.syncPending = taskUpdate.syncPending ?? false;

          // Update related collections if provided
          if (taskUpdate.taskalerts != null) {
            existingTask.taskalerts.clear();
            final tempTaskModel = TaskDetailMapper.toModel(taskUpdate, 0);
            existingTask.taskalerts.addAll(tempTaskModel.taskalerts);
          }
          if (taskUpdate.followupTasks != null) {
            existingTask.followupTasks.clear();
            final tempTaskModel = TaskDetailMapper.toModel(taskUpdate, 0);
            existingTask.followupTasks.addAll(tempTaskModel.followupTasks);
          }
          if (taskUpdate.stocktake != null) {
            existingTask.stocktake.clear();
            final tempTaskModel = TaskDetailMapper.toModel(taskUpdate, 0);
            existingTask.stocktake.addAll(tempTaskModel.stocktake);
          }
          if (taskUpdate.posItems != null) {
            existingTask.posItems.clear();
            final tempTaskModel = TaskDetailMapper.toModel(taskUpdate, 0);
            existingTask.posItems.addAll(tempTaskModel.posItems);
          }
          if (taskUpdate.resumePauseItems != null) {
            existingTask.resumePauseItems.clear();
            final tempTaskModel = TaskDetailMapper.toModel(taskUpdate, 0);
            existingTask.resumePauseItems
                .addAll(tempTaskModel.resumePauseItems);
          }

          logger('📋 Updated task: $taskId');
        } else {
          logger('⚠️ Task not found for task update: $taskId');
        }
      }
    });
  }

  /// Process update_tasks_photos: Update photo-related data for existing tasks
  static Future<void> _processUpdateTasksPhotos(
      Realm realm, List<TaskDetail>? updateTasksPhotos) async {
    if (updateTasksPhotos == null || updateTasksPhotos.isEmpty) return;

    logger('📸 Processing ${updateTasksPhotos.length} photo updates');

    realm.write(() {
      for (final taskUpdate in updateTasksPhotos) {
        final taskId = taskUpdate.taskId?.toInt();
        if (taskId == null) continue;

        final existingTask =
            realm.query<TaskDetailModel>('taskId == \$0', [taskId]).firstOrNull;
        if (existingTask != null) {
          // Update photo-related fields
          existingTask.photoFolder.clear();
          if (taskUpdate.photoFolder != null) {
            final tempTaskModel = TaskDetailMapper.toModel(taskUpdate, 0);
            existingTask.photoFolder.addAll(tempTaskModel.photoFolder);
          }
          existingTask.modifiedTimeStampPhotos =
              taskUpdate.modifiedTimeStampPhotos;
          existingTask.modifiedTimeStampPhototypes =
              taskUpdate.modifiedTimeStampPhototypes;
          existingTask.photoResPerc = taskUpdate.photoResPerc?.toInt();
          existingTask.liveImagesOnly = taskUpdate.liveImagesOnly;

          logger('📸 Updated photos for task: $taskId');
        } else {
          logger('⚠️ Task not found for photo update: $taskId');
        }
      }
    });
  }

  /// Process add_tasks: Add new tasks to local database
  static Future<void> _processAddTasks(
      Realm realm, List<TaskDetail>? addTasks) async {
    if (addTasks == null || addTasks.isEmpty) return;

    logger('➕ Processing ${addTasks.length} new tasks');

    realm.write(() {
      int nextId = _getNextTaskModelId(realm);

      for (final newTask in addTasks) {
        final taskId = newTask.taskId?.toInt();
        if (taskId == null) continue;

        // Check if task already exists
        final existingTask =
            realm.query<TaskDetailModel>('taskId == \$0', [taskId]).firstOrNull;
        if (existingTask == null) {
          // Add new task
          final taskModel = TaskDetailMapper.toModel(newTask, nextId++);
          realm.add(taskModel);
          logger('➕ Added new task: $taskId');
        } else {
          logger('⚠️ Task already exists, skipping: $taskId');
        }
      }
    });
  }

  /// Process update_tasks_submission: Update submission-related data for existing tasks
  static Future<void> _processUpdateTasksSubmission(
      Realm realm, List<TaskDetail>? updateTasksSubmission) async {
    if (updateTasksSubmission == null || updateTasksSubmission.isEmpty) return;

    logger('📤 Processing ${updateTasksSubmission.length} submission updates');

    realm.write(() {
      for (final taskUpdate in updateTasksSubmission) {
        final taskId = taskUpdate.taskId?.toInt();
        if (taskId == null) continue;

        final existingTask =
            realm.query<TaskDetailModel>('taskId == \$0', [taskId]).firstOrNull;
        if (existingTask != null) {
          // Update submission-related fields
          existingTask.submissionTimeStamp = taskUpdate.submissionTimeStamp;
          existingTask.submissionState = taskUpdate.submissionState;
          existingTask.taskStatus = taskUpdate.taskStatus;
          existingTask.syncPending = taskUpdate.syncPending ?? false;

          logger('📤 Updated submission for task: $taskId');
        } else {
          logger('⚠️ Task not found for submission update: $taskId');
        }
      }
    });
  }

  /// Process update_tasks_signatures: Update signature-related data for existing tasks
  static Future<void> _processUpdateTasksSignatures(
      Realm realm, List<TaskDetail>? updateTasksSignatures) async {
    if (updateTasksSignatures == null || updateTasksSignatures.isEmpty) return;

    logger('✍️ Processing ${updateTasksSignatures.length} signature updates');

    realm.write(() {
      for (final taskUpdate in updateTasksSignatures) {
        final taskId = taskUpdate.taskId?.toInt();
        if (taskId == null) continue;

        final existingTask =
            realm.query<TaskDetailModel>('taskId == \$0', [taskId]).firstOrNull;
        if (existingTask != null) {
          // Update signature-related fields
          existingTask.signatureFolder.clear();
          if (taskUpdate.signatureFolder != null) {
            final tempTaskModel = TaskDetailMapper.toModel(taskUpdate, 0);
            existingTask.signatureFolder.addAll(tempTaskModel.signatureFolder);
          }
          existingTask.modifiedTimeStampSignatures =
              taskUpdate.modifiedTimeStampSignatures;
          existingTask.modifiedTimeStampSignaturetypes =
              taskUpdate.modifiedTimeStampSignaturetypes;

          logger('✍️ Updated signatures for task: $taskId');
        } else {
          logger('⚠️ Task not found for signature update: $taskId');
        }
      }
    });
  }

  /// Process upload_task_ids: Update sync status for uploaded tasks
  static Future<void> _processUploadTaskIds(
      Realm realm, List<String>? uploadTaskIds) async {
    if (uploadTaskIds == null || uploadTaskIds.isEmpty) return;

    logger('📤 Processing ${uploadTaskIds.length} uploaded task IDs');

    realm.write(() {
      for (final taskIdStr in uploadTaskIds) {
        final taskId = int.tryParse(taskIdStr);
        if (taskId == null) {
          logger('⚠️ Invalid task ID format: $taskIdStr');
          continue;
        }

        final task =
            realm.query<TaskDetailModel>('taskId == \$0', [taskId]).firstOrNull;
        if (task != null) {
          // Mark task as successfully uploaded/synced
          task.syncPending = false;
          task.isSynced = true;
          logger('📤 Marked task as uploaded: $taskId');
        } else {
          logger('⚠️ Task not found for upload status update: $taskId');
        }
      }
    });
  }

  /// Helper method to get the next available ID for TaskDetailModel
  static int _getNextTaskModelId(Realm realm) {
    final allTasks = realm.all<TaskDetailModel>();
    if (allTasks.isEmpty) return 1;

    final maxId =
        allTasks.map((task) => task.id).reduce((a, b) => a > b ? a : b);
    return maxId + 1;
  }

  /// Create getTasks request entity from database tasks
  ///
  /// This method prepares the request body for the getTasks API,
  /// following the exact structure and parameters from downloadTaskDataSimplified()
  /// method in lib/syncing.dart. It creates a TasksRequestEntity with all necessary
  /// task data including timestamps and metadata.
  ///
  /// Returns a properly structured TasksRequestEntity for the /tasks_optimize endpoint
  static Future<TasksRequestEntity> createGetTasksRequest() async {
    final realm = sl<RealmDatabase>().realm;
    final tasks = realm.all<TaskDetailModel>();
    final tasksPost = tasks.map((e) => TaskDetailMapper.toEntity(e)).toList();
    final dataManager = sl<DataManager>();
    final userId = await dataManager.getUserId() ?? "";
    final token = await dataManager.getAuthToken() ?? "";

    // Build task list with all required fields
    final List<Task> taskList = [];
    for (var task in tasksPost) {
      var taskItem = Task();
      taskItem.taskId = task.taskId?.toString();
      taskItem.scheduledTimeStamp = task.scheduledTimeStamp;
      taskItem.submissionTimeStamp = task.submissionTimeStamp;
      taskItem.modifiedTimeStampTask = task.modifiedTimeStampTask;
      taskItem.modifiedTimeStampPhotos = task.modifiedTimeStampPhotos;
      taskItem.modifiedTimeStampSignatures = task.modifiedTimeStampSignatures;
      taskItem.modifiedTimeStampSignaturetypes =
          task.modifiedTimeStampSignaturetypes;
      taskItem.modifiedTimeStampPhototypes = task.modifiedTimeStampPhototypes;
      taskItem.modifiedTimeStampForms = task.modifiedTimeStampForms;
      taskItem.modifiedTimeStampDocuments = task.modifiedTimeStampDocuments;
      taskItem.phototypeIds = [];
      taskItem.forceImportFollowupTask = false;
      taskList.add(taskItem);
    }

    // Create a new TasksRequestEntity with the populated task list
    return TasksRequestEntity(
      deviceUid: await dataManager.getOrCreateDeviceId(),
      userId: userId,
      appversion: appVersion,
      tasks: taskList,
      token: token,
    );
  }

  /// Process update_phototypes: Update photo type information for existing tasks
  static Future<void> _processUpdatePhototypes(
      Realm realm, List<PhotoTypeListModel>? updatePhototypes) async {
    if (updatePhototypes == null || updatePhototypes.isEmpty) return;

    logger('📷 Processing ${updatePhototypes.length} photo type updates');

    realm.write(() {
      for (final photoTypeUpdate in updatePhototypes) {
        final taskId = int.tryParse(photoTypeUpdate.taskId ?? "");
        if (taskId == null) continue;

        final existingTask =
            realm.query<TaskDetailModel>('taskId == \$0', [taskId]).firstOrNull;
        if (existingTask != null) {
          // Update photo type information for each photo type
          for (final serverPhotoType in photoTypeUpdate.photoTypes ?? []) {
            final photoTypeId = serverPhotoType.phototype_id;

            // Find the corresponding photo folder
            final localPhotoFolder = existingTask.photoFolder
                .where((folder) => folder.folderId.toString() == photoTypeId)
                .firstOrNull;

            if (localPhotoFolder != null) {
              // Update photo folder properties based on photo type
              localPhotoFolder.attribute = serverPhotoType.isManadatory();
              localPhotoFolder.folderName = serverPhotoType.phototype_name;
              localPhotoFolder.folderPictureAmount =
                  serverPhotoType.phototype_picture_amount;
              localPhotoFolder.modifiedTimeStampPhototype =
                  serverPhotoType.modified_time_stamp_phototype;
            }
          }

          // Update task's photo types modified timestamp
          existingTask.modifiedTimeStampPhototypes =
              photoTypeUpdate.modifiedTimeStampPhototypes;

          logger('📷 Updated photo types for task: $taskId');
        } else {
          logger('⚠️ Task not found for photo type update: $taskId');
        }
      }
    });
  }

  /// Process add_phototypes: Add new photo types to existing tasks
  static Future<void> _processAddPhototypes(
      Realm realm, List<PhotoTypeListModel>? addPhototypes) async {
    if (addPhototypes == null || addPhototypes.isEmpty) return;

    logger('📷➕ Processing ${addPhototypes.length} photo type additions');

    realm.write(() {
      for (final photoTypeAdd in addPhototypes) {
        final taskId = int.tryParse(photoTypeAdd.taskId ?? "");
        if (taskId == null) continue;

        final existingTask =
            realm.query<TaskDetailModel>('taskId == \$0', [taskId]).firstOrNull;
        if (existingTask != null) {
          // Add new photo types as photo folders
          for (final serverPhotoType in photoTypeAdd.photoTypes ?? []) {
            final photoTypeId = serverPhotoType.phototype_id;

            // Check if photo folder already exists
            final existingFolder = existingTask.photoFolder
                .where((folder) => folder.folderId.toString() == photoTypeId)
                .firstOrNull;

            if (existingFolder == null) {
              // Create new photo folder for this photo type
              final newPhotoFolder = PhotoFolderModel();
              newPhotoFolder.folderId = int.tryParse(photoTypeId);
              newPhotoFolder.attribute = serverPhotoType.isManadatory();
              newPhotoFolder.folderName = serverPhotoType.phototype_name;
              newPhotoFolder.folderPictureAmount =
                  serverPhotoType.phototype_picture_amount;
              newPhotoFolder.modifiedTimeStampPhototype =
                  serverPhotoType.modified_time_stamp_phototype;

              // Add to task's photo folders
              existingTask.photoFolder.add(newPhotoFolder);

              logger('📷➕ Added new photo type $photoTypeId for task: $taskId');
            } else {
              logger(
                  '⚠️ Photo type $photoTypeId already exists for task: $taskId');
            }
          }

          // Update task's photo types modified timestamp
          existingTask.modifiedTimeStampPhototypes =
              photoTypeAdd.modifiedTimeStampPhototypes;
        } else {
          logger('⚠️ Task not found for photo type addition: $taskId');
        }
      }
    });
  }

  /// Process delete_phototypes: Delete photo types from existing tasks
  static Future<void> _processDeletePhototypes(
      Realm realm, List<PhotoTypeDeleteListModel>? deletePhototypes) async {
    if (deletePhototypes == null || deletePhototypes.isEmpty) return;

    logger('📷🗑️ Processing ${deletePhototypes.length} photo type deletions');

    realm.write(() {
      for (final photoTypeDelete in deletePhototypes) {
        final taskId = int.tryParse(photoTypeDelete.taskId ?? "");
        if (taskId == null) continue;

        final existingTask =
            realm.query<TaskDetailModel>('taskId == \$0', [taskId]).firstOrNull;
        if (existingTask != null) {
          // Delete photo types by removing corresponding photo folders
          for (final photoTypeId in photoTypeDelete.phototypeIDsToBeDeleted) {
            final folderToDelete = existingTask.photoFolder
                .where((folder) => folder.folderId.toString() == photoTypeId)
                .firstOrNull;

            if (folderToDelete != null) {
              // Delete all photos in the folder first
              folderToDelete.photos.clear();
              // Remove the folder from the task
              existingTask.photoFolder.remove(folderToDelete);

              logger('📷🗑️ Deleted photo type $photoTypeId for task: $taskId');
            } else {
              logger(
                  '⚠️ Photo type $photoTypeId not found for deletion in task: $taskId');
            }
          }
        } else {
          logger('⚠️ Task not found for photo type deletion: $taskId');
        }
      }
    });
  }

  /// Process update_signaturetypes: Update signature type information for existing tasks
  static Future<void> _processUpdateSignatureTypes(
      Realm realm, List<SignatureTypeListModel>? updateSignatureTypes) async {
    if (updateSignatureTypes == null || updateSignatureTypes.isEmpty) return;

    logger(
        '✍️ Processing ${updateSignatureTypes.length} signature type updates');

    realm.write(() {
      for (final signatureTypeUpdate in updateSignatureTypes) {
        final taskId = int.tryParse(signatureTypeUpdate.taskId ?? "");
        if (taskId == null) continue;

        final existingTask =
            realm.query<TaskDetailModel>('taskId == \$0', [taskId]).firstOrNull;
        if (existingTask != null) {
          // Update signature type information for each signature type
          for (final serverSignatureType
              in signatureTypeUpdate.signatureTypes ?? []) {
            final signatureTypeId = serverSignatureType.signaturetype_id;

            // Find the corresponding signature folder
            final localSignatureFolder = existingTask.signatureFolder
                .where(
                    (folder) => folder.folderId.toString() == signatureTypeId)
                .firstOrNull;

            if (localSignatureFolder != null) {
              // Update signature folder properties based on signature type
              localSignatureFolder.attribute =
                  serverSignatureType.isManadatory();
              localSignatureFolder.folderName =
                  serverSignatureType.signaturetype_name;
              localSignatureFolder.modifiedTimeStampSignaturetype =
                  serverSignatureType.modified_time_stamp_signaturetype;
            }
          }

          // Update task's signature types modified timestamp
          existingTask.modifiedTimeStampSignaturetypes =
              signatureTypeUpdate.modifiedTimeStampSignaturetypes;

          logger('✍️ Updated signature types for task: $taskId');
        } else {
          logger('⚠️ Task not found for signature type update: $taskId');
        }
      }
    });
  }

  /// Process add_signaturetypes: Add new signature types to existing tasks
  static Future<void> _processAddSignatureTypes(
      Realm realm, List<SignatureTypeListModel>? addSignatureTypes) async {
    if (addSignatureTypes == null || addSignatureTypes.isEmpty) return;

    logger(
        '✍️➕ Processing ${addSignatureTypes.length} signature type additions');

    realm.write(() {
      for (final signatureTypeAdd in addSignatureTypes) {
        final taskId = int.tryParse(signatureTypeAdd.taskId ?? "");
        if (taskId == null) continue;

        final existingTask =
            realm.query<TaskDetailModel>('taskId == \$0', [taskId]).firstOrNull;
        if (existingTask != null) {
          // Add new signature types as signature folders
          for (final serverSignatureType
              in signatureTypeAdd.signatureTypes ?? []) {
            final signatureTypeId = serverSignatureType.signaturetype_id;

            // Check if signature folder already exists
            final existingFolder = existingTask.signatureFolder
                .where(
                    (folder) => folder.folderId.toString() == signatureTypeId)
                .firstOrNull;

            if (existingFolder == null) {
              // Create new signature folder for this signature type
              final newSignatureFolder = SignatureFolderModel();
              newSignatureFolder.folderId = int.tryParse(signatureTypeId);
              newSignatureFolder.attribute = serverSignatureType.isManadatory();
              newSignatureFolder.folderName =
                  serverSignatureType.signaturetype_name;
              newSignatureFolder.modifiedTimeStampSignaturetype =
                  serverSignatureType.modified_time_stamp_signaturetype;

              // Add to task's signature folders
              existingTask.signatureFolder.add(newSignatureFolder);

              logger(
                  '✍️➕ Added new signature type $signatureTypeId for task: $taskId');
            } else {
              logger(
                  '⚠️ Signature type $signatureTypeId already exists for task: $taskId');
            }
          }

          // Update task's signature types modified timestamp
          existingTask.modifiedTimeStampSignaturetypes =
              signatureTypeAdd.modifiedTimeStampSignaturetypes;
        } else {
          logger('⚠️ Task not found for signature type addition: $taskId');
        }
      }
    });
  }

  /// Process delete_signaturetypes: Delete signature types from existing tasks
  static Future<void> _processDeleteSignatureTypes(Realm realm,
      List<SignatureTypeDeleteListModel>? deleteSignatureTypes) async {
    if (deleteSignatureTypes == null || deleteSignatureTypes.isEmpty) return;

    logger(
        '✍️🗑️ Processing ${deleteSignatureTypes.length} signature type deletions');

    realm.write(() {
      for (final signatureTypeDelete in deleteSignatureTypes) {
        final taskId = int.tryParse(signatureTypeDelete.taskId ?? "");
        if (taskId == null) continue;

        final existingTask =
            realm.query<TaskDetailModel>('taskId == \$0', [taskId]).firstOrNull;
        if (existingTask != null) {
          // Delete signature types by removing corresponding signature folders
          for (final signatureTypeId
              in signatureTypeDelete.signaturetypeIDsToBeDeleted) {
            final folderToDelete = existingTask.signatureFolder
                .where(
                    (folder) => folder.folderId.toString() == signatureTypeId)
                .firstOrNull;

            if (folderToDelete != null) {
              // Delete all signatures in the folder first
              folderToDelete.signatures.clear();
              // Remove the folder from the task
              existingTask.signatureFolder.remove(folderToDelete);

              logger(
                  '✍️🗑️ Deleted signature type $signatureTypeId for task: $taskId');
            } else {
              logger(
                  '⚠️ Signature type $signatureTypeId not found for deletion in task: $taskId');
            }
          }
        } else {
          logger('⚠️ Task not found for signature type deletion: $taskId');
        }
      }
    });
  }
}
