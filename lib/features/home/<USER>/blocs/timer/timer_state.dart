import 'package:equatable/equatable.dart';

abstract class TimerState extends Equatable {
  const TimerState();

  @override
  List<Object?> get props => [];
}

class TimerInitial extends TimerState {}

class TimerRunning extends TimerState {
  final int taskId;
  final DateTime startTime;
  final Duration elapsed;
  final bool canPause;
  final bool canStop;

  const TimerRunning({
    required this.taskId,
    required this.startTime,
    required this.elapsed,
    this.canPause = true,
    this.canStop = true,
  });

  @override
  List<Object?> get props => [taskId, startTime, elapsed, canPause, canStop];
}

class TimerPaused extends TimerState {
  final int taskId;
  final DateTime startTime;
  final Duration elapsed;
  final bool canResume;
  final bool canStop;

  const TimerPaused({
    required this.taskId,
    required this.startTime,
    required this.elapsed,
    this.canResume = true,
    this.canStop = true,
  });

  @override
  List<Object?> get props => [taskId, startTime, elapsed, canResume, canStop];
}

class TimerStopped extends TimerState {
  final int taskId;
  final Duration totalElapsed;
  final bool canStart;

  const TimerStopped({
    required this.taskId,
    required this.totalElapsed,
    this.canStart = true,
  });

  @override
  List<Object?> get props => [taskId, totalElapsed, canStart];
}

class TimerError extends TimerState {
  final String message;

  const TimerError(this.message);

  @override
  List<Object?> get props => [message];
}