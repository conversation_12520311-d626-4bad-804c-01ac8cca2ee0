import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gap/gap.dart';
import 'package:intl/intl.dart';
import 'package:storetrack_app/config/themes/app_colors.dart';
import 'package:storetrack_app/core/extensions/theme_extensions.dart';
import 'package:storetrack_app/core/utils/snackbar_service.dart';
import 'package:storetrack_app/di/service_locator.dart';
import 'package:storetrack_app/features/auth/data/models/login_response.dart';
import 'package:storetrack_app/features/home/<USER>/blocs/dashboard/dashboard_cubit.dart';
import 'package:storetrack_app/features/home/<USER>/blocs/dashboard/dashboard_state.dart';
import 'package:storetrack_app/features/home/<USER>/entities/tasks_request_entity.dart';
import 'package:storetrack_app/shared/widgets/odometer_input_dialog.dart';
import 'package:storetrack_app/shared/widgets/elapsed_time_dialog.dart';
import 'package:storetrack_app/shared/widgets/confirm_dialog.dart';
import 'package:storetrack_app/shared/widgets/offline_indicator.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:storetrack_app/shared/cubits/sync_cubit.dart';

import '../../../../config/routes/app_router.gr.dart';
import '../../../../core/constants/app_assets.dart';
import '../../../../core/storage/data_manager.dart';
import '../widgets/dashboard_item.dart';

@RoutePage()
class DashboardPage extends StatefulWidget {
  const DashboardPage({super.key});

  @override
  State<DashboardPage> createState() => _DashboardPageState();
}

class _DashboardPageState extends State<DashboardPage> {
  bool _isDayStarted = false;
  String actualDeviceUid = '';
  late String actualUserId;
  final String actualAppVersion = "9.9.9";
  final List<String> actualTasksToUnschedule = [];
  late String actualUserToken;

  // Task count variables
  int countUnscheduled = 0;
  int countScheduled = 0;
  int countPos = 0;
  int countCompleted = 0;
  int countToday = 0;
  double completionPercentage = 0.0;

  bool? isPriceCheckerUser;
  bool? isAdminUniversal;
  bool? dayCheckIn;
  bool? pos;
  bool? openTasks;
  bool? vacancies;
  bool? premAutoSchedule;
  bool? premAvailability;
  bool? premAutoSchedule4Weeks;
  bool? watermarkImages;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      precacheImage(const AssetImage(AppAssets.bgImage), context);
    });
    _setTiles();
    _initializeData();
    _loadDayStatus();
  }

  Future<void> _loadDayStatus() async {
    final dayStarted = await context.read<DashboardCubit>().getDayStarted();
    setState(() {
      _isDayStarted = dayStarted;
    });
  }

  Future<void> _onStartEndDayButtonPressed() async {
    print('onStartEndDayButtonPressed');
    final cubit = context.read<DashboardCubit>();

    if (_isDayStarted) {
      // Day is already started, check if need to handle previous day
      final isToday = await cubit.isStartTimeToday();
      if (!isToday) {
        // Start time is from yesterday or earlier → Forgot to end session flow
        _showForgotToEndDayDialog();
      } else {
        // Start time is from today → Normal end day flow
        _showOdometerInputDialog();
      }
    } else {
      // Day not started yet, show input dialog to start
      _showOdometerInputDialog();
    }
  }

  void _showOdometerInputDialog() {
    final title = _isDayStarted ? 'End day' : 'Start day';
    final message = _isDayStarted
        ? 'Enter your ending odometer reading to end your workday.'
        : 'Enter your starting odometer reading to begin your workday.';

    OdometerInputDialog.show(
      context: context,
      title: title,
      message: message,
      onConfirm: (odometerReading) {
        context
            .read<DashboardCubit>()
            .startEndDay(odometerReading: odometerReading);
      },
    );
  }

  void _showForgotToEndDayDialog() async {
    final cubit = context.read<DashboardCubit>();
    final formattedStartTime = await cubit.getFormattedStartTime();

    ConfirmDialog.show(
      context: context,
      title: 'Forgot to end last session',
      message:
          'The session you started on: ${formattedStartTime ?? 'previous day'} is still active. Press OK to end session.',
      confirmText: 'OK',
      onConfirm: () {
        cubit.forceEndPreviousDay();
      },
    );
  }

  void _handleCheckinStateChange(DashboardState state) {
    if (state is CheckinSuccess) {
      setState(() {
        _isDayStarted = !state.isDayEnded;
      });

      if (state.isDayEnded && state.elapsedTime != null) {
        // Show elapsed time dialog for day end
        ElapsedTimeDialog.show(
          context: context,
          elapsedTime: state.elapsedTime!,
        );
      }

      // Show success message
      final message = state.isDayEnded
          ? 'Day ended successfully!'
          : 'Day started successfully!';

      SnackBarService.success(
        context: context,
        message: message,
      );
    } else if (state is CheckinError) {
      SnackBarService.error(
        context: context,
        message: state.message,
      );
    }
  }

  Widget _buildStartEndDayButton() {
    print('buildStartEndDayButton');
    return ElevatedButton(
      onPressed: _onStartEndDayButtonPressed,
      style: ElevatedButton.styleFrom(
        backgroundColor: _isDayStarted ? Colors.red : AppColors.primaryBlue,
        foregroundColor: Colors.white,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8),
        ),
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
        minimumSize: const Size(0, 32),
      ),
      child: Text(
        _isDayStarted ? 'End day' : 'Start day',
        style: const TextStyle(
          fontSize: 12,
          fontWeight: FontWeight.w600,
          color: Colors.white,
        ),
      ),
    );
  }

  Future<void> _setTiles() async {
    LoginResponse? loginResponse = await sl<DataManager>().getLoginResponse();
    if (loginResponse != null) {
      setState(() {
        isPriceCheckerUser = loginResponse.data?.isPriceCheckerUser;
        isAdminUniversal = loginResponse.data?.isAdminUniversal;
        dayCheckIn = loginResponse.data?.dayCheckIn;
        pos = loginResponse.data?.pos;
        openTasks = loginResponse.data?.openTasks;
        vacancies = loginResponse.data?.vacancies;
        premAutoSchedule = loginResponse.data?.premAutoSchedule;
        premAvailability = loginResponse.data?.premAvailability;
        premAutoSchedule4Weeks = loginResponse.data?.premAutoSchedule4Weeks;
        watermarkImages = loginResponse.data?.watermarkImages;
      });
    }
  }

  Future<void> _initializeData() async {
    try {
      // Get user ID, token, and device ID from DataManager
      final dataManager = sl<DataManager>();
      actualUserId = await dataManager.getUserId() ?? "0";
      actualUserToken = await dataManager.getAuthToken() ?? "0";
      actualDeviceUid = await dataManager.getOrCreateDeviceId();

      // Fetch unscheduled tasks
      if (mounted) {
        context.read<DashboardCubit>().fetchDashboardData(
              TasksRequestEntity(
                deviceUid: actualDeviceUid,
                userId: actualUserId,
                appversion: actualAppVersion,
                tasks: const [],
                token: actualUserToken,
              ),
            );
      }
    } catch (e) {
      if (mounted) {
        SnackBarService.error(
          context: context,
          message: 'Failed to initialize dashboard: ${e.toString()}',
        );
      }
    }
  }

  Future<void> _refreshDashboardData() async {
    try {
      // Fetch dashboard data again
      if (mounted) {
        await context.read<DashboardCubit>().fetchDashboardData(
              TasksRequestEntity(
                deviceUid: actualDeviceUid,
                userId: actualUserId,
                appversion: actualAppVersion,
                tasks: const [],
                token: actualUserToken,
              ),
            );
      }
      return;
    } catch (e) {
      if (mounted) {
        SnackBarService.error(
          context: context,
          message: 'Failed to refresh dashboard: ${e.toString()}',
        );
      }
      // Re-throw the error to indicate the refresh failed
      rethrow;
    }
  }

  /// Shows a support popup dialog with email and create ticket options
  Future<void> _showSupportPopup() async {
    return showDialog<void>(
      context: context,
      barrierDismissible: true,
      builder: (BuildContext context) {
        return Dialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          elevation: 0,
          backgroundColor: Colors.transparent,
          child: Container(
            padding: const EdgeInsets.all(20),
            decoration: BoxDecoration(
              color: Colors.white,
              shape: BoxShape.rectangle,
              borderRadius: BorderRadius.circular(16),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.1),
                  blurRadius: 10,
                  offset: const Offset(0, 4),
                ),
              ],
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  'How do you want to contact support?',
                  style:
                      Theme.of(context).textTheme.montserratTitleSmall.copyWith(
                            fontWeight: FontWeight.w600,
                            color: Colors.black87,
                          ),
                ),
                const SizedBox(height: 16),
                Text(
                  'Choose an option to get support assistance.',
                  textAlign: TextAlign.center,
                  style: Theme.of(context)
                      .textTheme
                      .montserratTitleExtraSmall
                      .copyWith(
                        color: AppColors.black.withAlpha(196),
                      ),
                ),
                const SizedBox(height: 24),
                Row(
                  mainAxisAlignment: MainAxisAlignment.end,
                  children: [
                    TextButton(
                      onPressed: () {
                        Navigator.of(context).pop();
                        _createTicket();
                      },
                      child: Text(
                        'Create Ticket',
                        style: Theme.of(context)
                            .textTheme
                            .montserratTitleExtraSmall
                            .copyWith(
                              color: AppColors.primaryBlue,
                            ),
                      ),
                    ),
                    const SizedBox(width: 8),
                    TextButton(
                      onPressed: () {
                        Navigator.of(context).pop();
                        _sendSupportEmail();
                      },
                      child: Text(
                        'Email',
                        style: Theme.of(context)
                            .textTheme
                            .montserratTitleExtraSmall
                            .copyWith(
                              color: AppColors.primaryBlue,
                            ),
                      ),
                    ),
                    // ElevatedButton(
                    //   onPressed: () {
                    //     Navigator.of(context).pop();
                    //     _sendSupportEmail();
                    //   },
                    //   style: ElevatedButton.styleFrom(
                    //     backgroundColor: AppColors.primaryBlue,
                    //     foregroundColor: Colors.white,
                    //     shape: RoundedRectangleBorder(
                    //       borderRadius: BorderRadius.circular(8),
                    //     ),
                    //     padding: const EdgeInsets.symmetric(
                    //       horizontal: 16,
                    //       vertical: 8
                    //     ),
                    //   ),
                    //   child: Text(
                    //     'Email',
                    //     style: Theme.of(context)
                    //         .textTheme
                    //         .montserratTitleExtraSmall
                    //         .copyWith(
                    //           color: Colors.white,
                    //         ),
                    //   ),
                    // ),
                  ],
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  /// Opens Gmail web page with pre-filled support email
  Future<void> _sendSupportEmail() async {
    final String subject = Uri.encodeComponent(
        'StoreTrack Issue[Flutter], Person ID : $actualUserId, App Version : $actualAppVersion');

    final String body = Uri.encodeComponent('Dear Support Team,\n\n'
        'I am experiencing an issue with the StoreTrack app.\n\n'
        'Details:\n'
        '- Person ID: $actualUserId\n'
        '- App Version: $actualAppVersion\n'
        '- Device UID: $actualDeviceUid\n\n'
        'Issue Description:\n'
        '[Please describe your issue here]\n\n'
        'Best regards');

    // Create Gmail compose URL
    final String gmailUrl =
        'https://mail.google.com/mail/?view=cm&fs=1&to=<EMAIL>&su=$subject&body=$body';

    try {
      final Uri gmailUri = Uri.parse(gmailUrl);

      if (await canLaunchUrl(gmailUri)) {
        await launchUrl(gmailUri, mode: LaunchMode.externalApplication);
      } else {
        // Fallback to WebBrowser if external launch fails
        if (mounted) {
          context.router.push(WebBrowserRoute(url: gmailUrl));
        }
      }
    } catch (e) {
      // Fallback to WebBrowser
      if (mounted) {
        context.router.push(WebBrowserRoute(url: gmailUrl));
      }
    }
  }

  /// Opens browser to create a support ticket
  Future<void> _createTicket() async {
    try {
      final Uri ticketUri = Uri.parse('https://nova.dksh.com');

      if (await canLaunchUrl(ticketUri)) {
        await launchUrl(ticketUri, mode: LaunchMode.externalApplication);
      } else {
        // Fallback to WebBrowser if external launch fails
        if (mounted) {
          context.router.push(WebBrowserRoute(url: 'https://nova.dksh.com'));
        }
      }
    } catch (e) {
      // Fallback to WebBrowser
      if (mounted) {
        context.router.push(WebBrowserRoute(url: 'https://nova.dksh.com'));
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;

    return BlocListener<SyncCubit, SyncState>(
      listener: (context, state) {
        if (state is SyncSuccess) {
          _refreshDashboardData();
        }
      },
      child: BlocConsumer<DashboardCubit, DashboardState>(
        listener: (context, state) {
          // Handle checkin state changes
          _handleCheckinStateChange(state);

          if (state is DashboardLoaded) {
            setState(() {
              countUnscheduled = state.countUnscheduled;
              countScheduled = state.countScheduled;
              countPos = state.countPos;
              countCompleted = state.countCompleted;
              countToday = state.countToday;

              // Calculate completion percentage
              final int totalTasks =
                  countUnscheduled + countScheduled + countCompleted;
              if (totalTasks > 0) {
                completionPercentage = countCompleted / totalTasks;
              } else {
                completionPercentage = 0.0;
              }
            });
          } else if (state is DashboardError) {
            SnackBarService.error(
              context: context,
              message: state.message,
            );
          }
        },
        builder: (context, state) {
          return Scaffold(
            appBar: AppBar(
              backgroundColor: Colors.transparent,
              elevation: 0,
              automaticallyImplyLeading: false,
              systemOverlayStyle: const SystemUiOverlayStyle(
                statusBarColor: Colors.transparent,
                statusBarIconBrightness: Brightness.light,
              ),
            ),
            extendBodyBehindAppBar: true,
            body: Container(
              decoration: const BoxDecoration(
                  color: Colors.white,
                  image: DecorationImage(
                      image: AssetImage(AppAssets.bgImage), fit: BoxFit.fill)),
              height: MediaQuery.of(context).size.height,
              width: double.infinity,
              child: RefreshIndicator(
                onRefresh: _refreshDashboardData,
                color: AppColors.primaryBlue,
                backgroundColor: Colors.white,
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Gap(32),
                      // Offline indicator
                      const OfflineIndicator(
                        textColor: Colors.white,
                        backgroundColor: Colors.transparent,
                        padding: EdgeInsets.symmetric(vertical: 8.0, horizontal: 16.0),
                      ),
                      Row(
                        children: [
                          Text(
                            DateFormat('EEEE d MMM')
                                .format(DateTime.now())
                                .toUpperCase(),
                            style: textTheme.montserratParagraphXsmall
                                .copyWith(color: Colors.white),
                          ),
                          const Spacer(),
                          // Start/End Day Button
                          if (dayCheckIn == true) _buildStartEndDayButton(),
                          const Gap(8),
                          Padding(
                            padding: const EdgeInsets.only(top: 6),
                            child: IconButton(
                              icon: const Icon(
                                Icons.help_outline,
                                color: Colors.white,
                                size: 24,
                              ),
                              onPressed: _showSupportPopup,
                              tooltip: 'Support',
                            ),
                          ),
                          const Gap(2),
                          GestureDetector(
                            onTap: () {
                              context.router.push(const NotificationsRoute());
                            },
                            child: Padding(
                              padding: const EdgeInsets.only(top: 6),
                              child: Image.asset(
                                AppAssets.dashboardNotification,
                                width: 24,
                              ),
                            ),
                          ),
                        ],
                      ),
                      Text(
                        'SUMMARY',
                        style: textTheme.montserratBold
                            .copyWith(color: Colors.white),
                      ),
                      const SizedBox(
                        height: 20,
                      ),
                    
                      // Show loading indicator or content
                      state is DashboardLoading
                          ? const Expanded(
                              child: Center(
                                child: Column(
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  children: [
                                    CircularProgressIndicator(),
                                  ],
                                ),
                              ),
                            )
                          : Expanded(
                              child: SingleChildScrollView(
                                physics: const AlwaysScrollableScrollPhysics(),
                                child: Column(
                                  children: [
                                    // Row with Cycle Completion and Today
                                    SizedBox(
                                      height: 96,
                                      width: double.infinity,
                                      child: Row(
                                        children: [
                                          Expanded(
                                            child: DashboardItem(
                                              title: 'Cycle Completion',
                                              value: '',
                                              progress: completionPercentage,
                                            ),
                                          ),
                                          const SizedBox(
                                            width: 16,
                                          ),
                                          Expanded(
                                            child: DashboardItem(
                                              title: 'Today',
                                              value: countToday.toString(),
                                              icon: Image.asset(
                                                AppAssets.dashboardToday,
                                                width: 28,
                                              ),
                                              ontap: () {
                                                context.navigateTo(
                                                    const TodayRoute());
                                              },
                                            ),
                                          ),
                                        ],
                                      ),
                                    ),
                                    // Grid with dashboard items
                                    GridView.count(
                                      padding: const EdgeInsets.only(top: 16),
                                      crossAxisCount: 3, // Fixed crossAxisCount
                                      crossAxisSpacing: 16,
                                      mainAxisSpacing: 16,
                                      childAspectRatio: 1.1,
                                      shrinkWrap:
                                          true, // Added shrinkWrap to fit in Column
                                      physics:
                                          const NeverScrollableScrollPhysics(), // Disable grid scrolling
                                      children: [
                                        if (premAutoSchedule == true)
                                          DashboardItem(
                                            title: 'Auto Schedule',
                                            icon: Image.asset(
                                              AppAssets.autoScheduleIcon,
                                              width: 28,
                                            ),
                                            ontap: () {
                                              context.router.push(
                                                  const AutoScheduleRoute());
                                            },
                                          ),
                                        DashboardItem(
                                          title: 'Unscheduled',
                                          value: countUnscheduled.toString(),
                                          ontap: () {
                                            // Use navigateNamedTo instead of push to navigate within the tab structure
                                            // This ensures the bottom navigation bar remains visible
                                            context.navigateTo(
                                                const UnscheduledRoute());
                                          },
                                          icon: Image.asset(
                                            AppAssets.unscheduledIcon,
                                            width: 28,
                                          ),
                                        ),
                                        DashboardItem(
                                          title: 'Scheduled',
                                          value: countScheduled.toString(),
                                          icon: Image.asset(
                                            AppAssets.scheduledIcon,
                                            width: 28,
                                          ),
                                          ontap: () {
                                            context.navigateTo(
                                                const ScheduleRoute());
                                          },
                                        ),
                                        if (pos == true)
                                          DashboardItem(
                                            title: 'POS',
                                            value: countPos.toString(),
                                            icon: Image.asset(
                                              AppAssets.dashboardPos,
                                              width: 20,
                                            ),
                                            ontap: () {
                                              context.navigateTo(
                                                  const UnscheduledPosTasksRoute());
                                            },
                                          ),
                                        DashboardItem(
                                          title: 'History',
                                          // value: countCompleted.toString(),
                                          icon: Image.asset(
                                            AppAssets.dashboardHistory,
                                            width: 26,
                                          ),
                                        ),
                                        DashboardItem(
                                          title: 'Completed',
                                          value: countCompleted.toString(),
                                          icon: Image.asset(
                                            AppAssets.taskComplete,
                                            width: 26,
                                          ),
                                          ontap: () {
                                            context.navigateTo(
                                                const CompletedTasksRoute());
                                          },
                                        ),
                                        if (openTasks == true)
                                          DashboardItem(
                                            title: 'Open Tasks',
                                            value: '0',
                                            icon: Image.asset(
                                              AppAssets.dashboardOpenTasks,
                                              width: 28,
                                            ),
                                            ontap: () {
                                              context.router
                                                  .push(const OpenTasksRoute());
                                            },
                                          ),
                                        if (vacancies == true)
                                          DashboardItem(
                                            title: 'Vacancies',
                                            icon: Image.asset(
                                              AppAssets.homeProfile,
                                              width: 26,
                                            ),
                                            ontap: () {
                                              context.router
                                                  .push(const VacanciesRoute());
                                            },
                                          ),
                                        if (isAdminUniversal == true)
                                          DashboardItem(
                                            title: 'Emulate',
                                            icon: Image.asset(
                                              AppAssets.emulateGridImage,
                                              width: 26,
                                              color: AppColors.black,
                                            ),
                                          ),
                                      ],
                                    ),
                                  ],
                                ),
                              ),
                            ),
                    ],
                  ),
                ),
              ),
            ),
          );
        },
      ),
    );
  }
}
