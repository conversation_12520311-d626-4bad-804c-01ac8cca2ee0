# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## App Overview

**StoreTrack** is an enterprise Flutter application for retail field workforce management. It enables field workers to complete store audits, manage tasks, capture photos/signatures, and sync data offline-first.

## Core Business Logic

**Primary Workflow**: Authentication → Task Download → Task Completion → Data Sync → Report Submission

**Key Features**:
- Complex form processing (QPMD system)
- Offline-first architecture with intelligent sync
- Photo/signature capture and management
- Task scheduling and calendar integration
- POS (Point of Sale) delivery tracking
- Store management and contact system

## Development Commands

```bash
flutter pub get                         # Install dependencies
flutter run                            # Run app
flutter analyze                        # Static analysis
flutter test                           # Run tests
dart run build_runner build            # Generate code (routes, realm models)
dart run build_runner build --delete-conflicting-outputs  # Force regenerate
```

## Architecture

### Clean Architecture with BLoC Pattern
```
lib/
├── core/              # Database, network, services, utilities
├── config/            # Routes, themes, app configuration
├── features/          # Feature modules (auth, home, etc.)
│   └── [feature]/
│       ├── data/         # Models, repositories, datasources
│       ├── domain/       # Entities, use cases, validators
│       └── presentation/ # Pages, widgets, cubits/blocs
├── shared/            # Shared widgets, models, utilities
├── di/               # Dependency injection (GetIt)
└── main.dart         # App entry point
```

### Critical Files
- `lib/di/service_locator.dart` - All dependency registration (490+ lines)
- `lib/config/routes/app_router.dart` - Navigation configuration
- `lib/core/database/realm_database.dart` - Database schema (20+ models)
- `lib/core/network/api_client.dart` - HTTP client with interceptors
- `lib/main.dart` - App entry with 18 BlocProviders

## Data Architecture

### Database (Realm) - Schema Version 8
**Core Entity Relationships**:
```
TaskDetailModel (Primary) - 98+ properties
├── PhotoFolderModel (1:N) → PhotoModel (1:N)
├── SignatureFolderModel (1:N) → SignatureModel (1:N)
├── FormModel (1:N) → QuestionModel (1:N) → MeasurementModel (1:N)
├── PosItemModel (1:N)
├── DocumentModel (1:N)
├── TaskalertModel (1:N)
└── TaskmemberModel (1:N)
```

### API Integration
**Base URL**: `https://webservice2.storetrack.com.au/api`

**Key Endpoints**:
- `POST /auth_storetrackNZ` - Authentication
- `POST /tasks_optimize` - Get task list
- `POST /submit_report_v4_11` - Submit reports
- `POST /send_task_pic_v4_11` - Upload photos
- `POST /send_task_sig_v4_11` - Upload signatures
- `POST /sync_pic_info_mpt` - Sync photo metadata
- `POST /sync_sig_info` - Sync signature metadata

## Form Processing (QPMD System)

**QPMD = Question Part Measurement Data**

### Form Widget Types
1. **Text Fields** (Types 1-2): Basic text input
2. **Checkboxes** (Type 3): Boolean selections
3. **Dropdowns** (Types 4-5): Single selection
4. **Multi-select** (Type 6): Multiple selections
5. **Counter** (Type 7): Numeric input
6. **Radio Buttons** (Type 8): Single choice
7. **Date Picker** (Type 9): Date selection

### Conditional Logic Engine
```dart
// Dynamic field visibility based on user selections
Map<int, bool> processConditionalLogic({
  required List<Measurement> measurements,
  required Map<int, dynamic> currentValues,
  required int changedMeasurementId,
  required dynamic newValue,
  required bool isMll,
});
```

### Form Features
- **Auto-save**: Continuous form progress saving
- **Real-time Validation**: Field validation with custom messages
- **Quiz Mode**: Answer validation for quiz forms
- **Multi-instance**: Multiple form instances per question
- **Conditional Logic**: Show/hide fields based on selections

## Sync System Architecture

### 4-Phase Sync Process
1. **Photo Upload & Sync**: Individual photo upload → metadata sync → local cleanup
2. **Signature Upload & Sync**: Similar to photos with duplicate detection
3. **Report Submission**: Batch task data submission
4. **Task & Calendar Sync**: Download latest tasks and calendar updates

### Sync Service Implementation
```dart
class SyncService {
  Future<void> syncPhotos() // Phase 1
  Future<void> syncSignatures() // Phase 2
  Future<void> submitReports() // Phase 3
  Future<void> syncTasks() // Phase 4
}
```

### Offline Capabilities
- **Local Database**: Realm with comprehensive schema
- **Intelligent Caching**: Multi-level caching strategy
- **Conflict Resolution**: Timestamp-based conflict handling
- **Sync Indicators**: Visual sync status throughout app

## Photo & Signature Management

### Photo Workflow
1. **Capture**: Camera integration with image picker
2. **Local Storage**: Temporary local file storage in device filesystem
3. **Metadata Creation**: PhotoModel with task linkage
4. **Batch Upload**: Optimized upload with duplicate detection
5. **Sync & Cleanup**: Remove local files post-sync

### Three-tier Photo Organization
```dart
PhotoFolder → PhotoTagsT → PhotoModel
```

### Key Features
- **Duplicate Detection**: Prevent duplicate uploads
- **Local Path Management**: Efficient local file handling
- **Edit Tracking**: Track local modifications
- **Compression**: Optimized file sizes for upload

## State Management

### BLoC Pattern Implementation
```dart
// Service registration in service_locator.dart
sl.registerLazySingleton<CubitName>(() => CubitImpl(...));
sl.registerFactory<CubitName>(() => CubitImpl(...));

// Usage in main.dart
MultiBlocProvider(
  providers: [
    BlocProvider(create: (_) => sl<AuthCubit>()),
    BlocProvider(create: (_) => sl<DashboardCubit>()),
    // ... 18 total providers
  ],
  child: MaterialApp.router(...)
)
```

### State Pattern
- **States**: Initial, Loading, Success, Error with Equatable
- **Cubits**: Business logic with emit() for state changes
- **Use Cases**: Single responsibility business operations

## Task Management System

### Task States
- **Open**: Available for assignment
- **Scheduled**: Assigned with specific time
- **In Progress**: Currently being worked on
- **Completed**: Finished but not submitted
- **Submitted**: Sent to server
- **Synced**: Fully synchronized

### Task Operations
- **Pause/Resume**: Task interruption handling via ResumePauseItemModel
- **Comments**: Task-specific notes and comments
- **Attachments**: Document and photo attachments
- **History**: Complete task history tracking

## Core Services

### Essential Services
```dart
// Registered in service_locator.dart
PhotoService      // Photo management with local/remote sync
SyncService       // Background synchronization
LocationService   // GPS and location tracking
CameraService     // Camera integration
BarcodeScannerService // QR/barcode scanning
RealmDatabase     // Local database instance
ApiClient         // HTTP client with interceptors
```

### Service Patterns
```dart
// Service registration
sl.registerLazySingleton<ServiceInterface>(() => ServiceImpl());

// Usage in use cases
class UseCase {
  final ServiceInterface service;
  UseCase(this.service);
}
```

## Validation & Business Rules

### Form Validation Types
```dart
// Range validation
if (value < min || value > max) {
  return 'Value must be between $min and $max';
}

// Required field validation
if (value == null || value.isEmpty) {
  return 'This field is required';
}

// Photo validation
if (photos.length < mandatoryPhototypesCount) {
  return 'At least $mandatoryPhototypesCount photo is required';
}

// Quiz validation
if (selectedOption.isAnswer != true) {
  return 'The answer you selected is incorrect';
}
```

### Business Rules
- **Mandatory Photos**: Configurable photo requirements per form
- **Conditional Logic**: Multi-level field dependencies
- **Task Completion**: All required fields must be completed
- **Sync Requirements**: Photos/signatures must sync before report submission

## Authentication & Authorization

### Microsoft Azure AD Integration
```dart
// MSAL configuration in assets/jsons/msal_config.json
// Authentication flow: Login → Token → API calls → Refresh
```

### Token Management
- **JWT Tokens**: Stored securely in TokenManager
- **Auto-refresh**: Automatic token refresh on expiry
- **Session Management**: Secure session handling

## Error Handling

### Result Pattern
```dart
abstract class Result<T> {
  const Result();
}

class Success<T> extends Result<T> {
  final T data;
  const Success(this.data);
}

class Failure<T> extends Result<T> {
  final String message;
  const Failure(this.message);
}
```

### Error Categories
- **Network Errors**: Connection and timeout handling
- **Validation Errors**: Form validation feedback
- **Sync Errors**: Data synchronization issues
- **Authentication Errors**: Login and session issues

## Testing Strategy

### Test Structure
```
test/
├── core/           # Core service tests
├── features/       # Feature-specific tests
├── shared/         # Shared widget tests
└── widget_test.dart # Main widget test
```

### Testing Patterns
- **Unit Tests**: Use cases and business logic
- **Widget Tests**: UI component testing
- **Cubit Tests**: State management testing
- **Integration Tests**: End-to-end workflows

## Code Generation

### Auto-generated Files
- `app_router.gr.dart` - Routes from `@AutoRouterConfig`
- `*.realm.dart` - Database models from `@RealmModel`
- Build runner required after schema changes

### Generation Commands
```bash
dart run build_runner build                           # Standard generation
dart run build_runner build --delete-conflicting-outputs  # Force regeneration
```

## Navigation

### Auto Route Configuration
```dart
@AutoRouterConfig(replaceInRouteName: 'Screen|Page,Route')
class AppRouter extends RootStackRouter {
  // 60+ routes defined
}
```

### Navigation Patterns
```dart
// Push route
context.router.push(RouteNameRoute());

// Replace route
context.router.pushAndClearStack(RouteNameRoute());

// Pop route
context.router.pop();
```

## Performance Optimizations

### Optimization Strategies
- **Lazy Loading**: On-demand data loading
- **Image Optimization**: Efficient image handling with caching
- **Database Optimization**: Indexed Realm queries
- **Memory Management**: Proper disposal of resources

### Caching System
- **Multi-level Caching**: Local and remote caching
- **Cache Invalidation**: Smart cache management
- **Offline Support**: Robust offline capabilities

## Development Guidelines

### Code Organization
1. **Reuse First**: Check existing Cubits, Use Cases, widgets before creating new
2. **Follow DI**: Register all dependencies in `service_locator.dart`
3. **Result Pattern**: Use `Result<T>` for error handling
4. **Immutable States**: Keep state classes immutable with Equatable
5. **Clean Architecture**: Respect layer boundaries
6. **Code Generation**: Run build_runner after model changes

### Naming Conventions
- Pages: `*_page.dart`
- Widgets: `*_widget.dart`
- Models: `*_model.dart`, `*_response.dart`, `*_request.dart`
- Entities: `*_entity.dart`
- Use Cases: `*_usecase.dart`
- Cubits: `*_cubit.dart` + `*_state.dart`

### Key Development Patterns
```dart
// Repository pattern
abstract class Repository {
  Future<Result<T>> getData();
}

// Use case pattern
class GetDataUseCase {
  final Repository repository;
  GetDataUseCase(this.repository);
  
  Future<Result<T>> call() => repository.getData();
}

// Cubit pattern
class DataCubit extends Cubit<DataState> {
  final GetDataUseCase useCase;
  DataCubit(this.useCase) : super(DataInitial());
  
  Future<void> loadData() async {
    emit(DataLoading());
    final result = await useCase();
    result.fold(
      (failure) => emit(DataError(failure.message)),
      (data) => emit(DataLoaded(data)),
    );
  }
}
```

## Critical Implementation Notes

### Realm Database
- **Schema Version**: Currently 8, increment on changes
- **Migration**: Automatic schema migrations supported
- **Relationships**: Complex nested object relationships
- **Queries**: Use proper indexing for performance

### Photo Management
- **Local Storage**: Photos stored in device filesystem
- **Metadata**: Photo models stored in Realm
- **Sync Status**: Track local, uploaded, and synced states
- **Cleanup**: Remove local files after successful sync

### Form System
- **Dynamic Forms**: Forms generated from server configuration
- **Validation**: Real-time validation with custom messages
- **Auto-save**: Continuous form progress saving
- **Conditional Logic**: Complex field dependencies

### Sync System
- **Sequential Sync**: Photos → Signatures → Reports → Tasks
- **Retry Logic**: Automatic retry on failures
- **Conflict Resolution**: Timestamp-based conflict handling
- **Status Tracking**: Visual sync indicators

This comprehensive guide provides all necessary information for effective development in the StoreTrack codebase, covering architecture, business logic, data flow, and implementation patterns.